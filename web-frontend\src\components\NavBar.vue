<template>
  <a-layout-header class="header">
    <div class="logo">量化交易系统</div>
    <a-menu
      v-model:selectedKeys="selectedKeys"
      theme="dark"
      mode="horizontal"
      :style="{ lineHeight: '64px' }"
    >
      <a-menu-item key="dashboard">
        <router-link to="/">仪表盘</router-link>
      </a-menu-item>

      <!-- AI分析菜单 -->
      <a-sub-menu key="ai">
        <template #title>
          <span>AI分析</span>
        </template>
        <a-menu-item key="ai-overview">
          <router-link to="/ai">AI概览</router-link>
        </a-menu-item>
        <a-menu-item key="ai-predict">
          <router-link to="/ai/predict">行情预测</router-link>
        </a-menu-item>
        <a-menu-item key="ai-signal">
          <router-link to="/ai/signal">交易信号</router-link>
        </a-menu-item>
        <a-menu-item key="ai-risk">
          <router-link to="/ai/risk">智能风控</router-link>
        </a-menu-item>
        <a-menu-item key="ai-risk-analysis">
          <router-link to="/ai/risk-analysis">AI风控分析</router-link>
        </a-menu-item>
        <a-menu-item key="ai-history">
          <router-link to="/ai/history">分析历史</router-link>
        </a-menu-item>
      </a-sub-menu>

      <!-- OKX交易菜单 -->
      <a-sub-menu key="okx">
        <template #title>
          <span>OKX交易</span>
        </template>
        <a-menu-item key="okx-positions">
          <router-link to="/okx/positions">持仓管理</router-link>
        </a-menu-item>
        <a-menu-item key="okx-leverage">
          <router-link to="/okx/leverage">杠杆设置</router-link>
        </a-menu-item>
        <a-menu-item key="okx-transfer">
          <router-link to="/okx/transfer">资金划转</router-link>
        </a-menu-item>
        <a-menu-item key="okx-orders">
          <router-link to="/okx/orders">历史订单</router-link>
        </a-menu-item>
        <a-menu-item key="real-trading">
          <router-link to="/trading/real">实盘交易</router-link>
        </a-menu-item>
        <a-menu-item key="advanced-charts">
          <router-link to="/charts/advanced">高级图表</router-link>
        </a-menu-item>
        <a-menu-item key="risk-management">
          <router-link to="/risk/management">风险管理</router-link>
        </a-menu-item>
      </a-sub-menu>

      <a-menu-item key="account">
        <router-link to="/account">账户管理</router-link>
      </a-menu-item>
    </a-menu>

    <div class="user-info">
      <a-dropdown>
        <span class="ant-dropdown-link" @click.prevent>
          {{ userStore.username }}
          <DownOutlined />
        </span>
        <template #overlay>
          <a-menu>
            <a-menu-item key="profile">
              <UserOutlined />
              个人资料
            </a-menu-item>
            <a-menu-item key="settings" @click="goToSettings">
              <SettingOutlined />
              API设置
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="logout" @click="logout">
              <LogoutOutlined />
              退出登录
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </a-layout-header>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../stores/user'
import { DownOutlined, UserOutlined, SettingOutlined, LogoutOutlined } from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const selectedKeys = ref(['dashboard'])

// 根据当前路由设置选中的菜单项
watch(
  () => route.path,
  (newPath) => {
    if (newPath === '/') {
      selectedKeys.value = ['dashboard']
    } else if (newPath.startsWith('/ai')) {
      if (newPath === '/ai') {
        selectedKeys.value = ['ai', 'ai-overview']
      } else if (newPath === '/ai/predict') {
        selectedKeys.value = ['ai', 'ai-predict']
      } else if (newPath === '/ai/signal') {
        selectedKeys.value = ['ai', 'ai-signal']
      } else if (newPath === '/ai/risk') {
        selectedKeys.value = ['ai', 'ai-risk']
      } else if (newPath === '/ai/risk-analysis') {
        selectedKeys.value = ['ai', 'ai-risk-analysis']
      } else if (newPath === '/ai/history') {
        selectedKeys.value = ['ai', 'ai-history']
      }
    } else if (newPath.startsWith('/okx')) {
      if (newPath === '/okx/positions') {
        selectedKeys.value = ['okx', 'okx-positions']
      } else if (newPath === '/okx/leverage') {
        selectedKeys.value = ['okx', 'okx-leverage']
      } else if (newPath === '/okx/transfer') {
        selectedKeys.value = ['okx', 'okx-transfer']
      } else if (newPath === '/okx/orders') {
        selectedKeys.value = ['okx', 'okx-orders']
      }
    } else if (newPath === '/account') {
      selectedKeys.value = ['account']
    }
  },
  { immediate: true }
)

function logout() {
  userStore.logout()
  router.push('/login')
}

function goToSettings() {
  router.push('/settings/api')
}
</script>

<style scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.logo {
  color: white;
  font-size: 18px;
  font-weight: bold;
  margin-right: 48px;
}

.user-info {
  color: white;
  margin-left: 24px;
}

.ant-dropdown-link {
  color: white;
  cursor: pointer;
}

.ant-dropdown-link:hover {
  color: #1890ff;
}
</style>