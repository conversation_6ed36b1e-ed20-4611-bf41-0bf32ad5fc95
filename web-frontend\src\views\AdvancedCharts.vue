<template>
  <div class="advanced-charts">
    <a-row :gutter="24">
      <!-- 主图表区域 -->
      <a-col :span="18">
        <a-card title="高级图表分析" style="margin-bottom: 24px;">
          <div class="chart-controls" style="margin-bottom: 16px;">
            <a-space>
              <a-select
                v-model:value="selectedSymbol"
                style="width: 200px"
                @change="onSymbolChange"
              >
                <a-select-option value="BTC-USDT-SWAP">BTC-USDT-SWAP</a-select-option>
                <a-select-option value="ETH-USDT-SWAP">ETH-USDT-SWAP</a-select-option>
                <a-select-option value="SOL-USDT-SWAP">SOL-USDT-SWAP</a-select-option>
              </a-select>

              <a-select
                v-model:value="selectedTimeframe"
                style="width: 120px"
                @change="onTimeframeChange"
              >
                <a-select-option value="1m">1分钟</a-select-option>
                <a-select-option value="5m">5分钟</a-select-option>
                <a-select-option value="15m">15分钟</a-select-option>
                <a-select-option value="1H">1小时</a-select-option>
                <a-select-option value="4H">4小时</a-select-option>
                <a-select-option value="1D">1天</a-select-option>
              </a-select>

              <a-button @click="refreshChart" :loading="loadingChart">
                刷新数据
              </a-button>

              <a-button @click="toggleIndicators">
                {{ showIndicators ? '隐藏指标' : '显示指标' }}
              </a-button>
            </a-space>
          </div>

          <!-- K线图容器 -->
          <div id="candlestick-chart" style="height: 500px; width: 100%;"></div>

          <!-- 技术指标图表 -->
          <div v-if="showIndicators" style="margin-top: 16px;">
            <a-row :gutter="16">
              <a-col :span="12">
                <div id="volume-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
              <a-col :span="12">
                <div id="rsi-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 交易信号 -->
        <a-card title="AI交易信号">
          <a-table
            :dataSource="tradingSignals"
            :columns="signalColumns"
            size="small"
            :pagination="{ pageSize: 5 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'signal'">
                <a-tag :color="record.signal === 'BUY' ? 'green' : record.signal === 'SELL' ? 'red' : 'orange'">
                  {{ record.signal }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'strength'">
                <a-progress
                  :percent="record.strength * 100"
                  size="small"
                  :stroke-color="record.strength > 0.7 ? '#52c41a' : record.strength > 0.4 ? '#faad14' : '#ff4d4f'"
                />
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 右侧信息面板 -->
      <a-col :span="6">
        <!-- 实时价格 -->
        <a-card title="实时行情" style="margin-bottom: 16px;">
          <div v-if="currentPrice">
            <a-statistic
              :title="selectedSymbol"
              :value="currentPrice.last"
              :precision="2"
              suffix="USDT"
              :value-style="{
                color: parseFloat(currentPrice.sodUtc8) >= 0 ? '#52c41a' : '#ff4d4f',
                fontSize: '24px'
              }"
            />
            <a-divider />
            <a-descriptions size="small" :column="1">
              <a-descriptions-item label="24h涨跌">
                <span :style="{ color: parseFloat(currentPrice.sodUtc8) >= 0 ? '#52c41a' : '#ff4d4f' }">
                  {{ currentPrice.sodUtc8 }}%
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="24h最高">{{ currentPrice.high24h }}</a-descriptions-item>
              <a-descriptions-item label="24h最低">{{ currentPrice.low24h }}</a-descriptions-item>
              <a-descriptions-item label="24h成交量">{{ currentPrice.vol24h }}</a-descriptions-item>
            </a-descriptions>
          </div>
          <a-skeleton v-else active />
        </a-card>

        <!-- 技术指标数值 -->
        <a-card title="技术指标" style="margin-bottom: 16px;">
          <a-descriptions size="small" :column="1">
            <a-descriptions-item label="RSI(14)">
              <span :style="{ color: technicalIndicators.rsi > 70 ? '#ff4d4f' : technicalIndicators.rsi < 30 ? '#52c41a' : '#1890ff' }">
                {{ technicalIndicators.rsi.toFixed(2) }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="MACD">
              <span :style="{ color: technicalIndicators.macd > 0 ? '#52c41a' : '#ff4d4f' }">
                {{ technicalIndicators.macd.toFixed(4) }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="MA20">{{ technicalIndicators.ma20.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="MA50">{{ technicalIndicators.ma50.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="布林带上轨">{{ technicalIndicators.bbUpper.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="布林带下轨">{{ technicalIndicators.bbLower.toFixed(2) }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 市场情绪 -->
        <a-card title="市场情绪">
          <div style="text-align: center;">
            <a-progress
              type="circle"
              :percent="marketSentiment.score"
              :stroke-color="marketSentiment.score > 60 ? '#52c41a' : marketSentiment.score > 40 ? '#faad14' : '#ff4d4f'"
            />
            <div style="margin-top: 16px;">
              <a-tag :color="marketSentiment.score > 60 ? 'green' : marketSentiment.score > 40 ? 'orange' : 'red'">
                {{ marketSentiment.label }}
              </a-tag>
            </div>
            <div style="margin-top: 8px; font-size: 12px; color: #666;">
              基于技术指标和成交量分析
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import axios from 'axios'
import * as echarts from 'echarts'

// 响应式数据
const selectedSymbol = ref('BTC-USDT-SWAP')
const selectedTimeframe = ref('1H')
const loadingChart = ref(false)
const showIndicators = ref(true)
const currentPrice = ref(null)
const chartData = ref([])
const tradingSignals = ref([])

// 技术指标数据
const technicalIndicators = ref({
  rsi: 50,
  macd: 0,
  ma20: 0,
  ma50: 0,
  bbUpper: 0,
  bbLower: 0
})

// 市场情绪
const marketSentiment = ref({
  score: 50,
  label: '中性'
})

// 交易信号表格列
const signalColumns = [
  { title: '时间', dataIndex: 'time', key: 'time' },
  { title: '信号', dataIndex: 'signal', key: 'signal' },
  { title: '价格', dataIndex: 'price', key: 'price' },
  { title: '强度', dataIndex: 'strength', key: 'strength' },
  { title: '描述', dataIndex: 'description', key: 'description' }
]

// 图表实例
let candlestickChart = null
let volumeChart = null
let rsiChart = null
let priceUpdateInterval = null

// 初始化图表
const initCharts = async () => {
  await nextTick()

  // 初始化K线图
  const candlestickContainer = document.getElementById('candlestick-chart')
  if (candlestickContainer && !candlestickChart) {
    candlestickChart = echarts.init(candlestickContainer)

    // K线图配置
    const candlestickOption = {
      title: {
        text: `${selectedSymbol.value} K线图`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['K线', 'MA5', 'MA10', 'MA20'],
        top: 30
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '15%'
      },
      xAxis: {
        type: 'category',
        data: [],
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        splitNumber: 20,
        min: 'dataMin',
        max: 'dataMax'
      },
      yAxis: {
        scale: true,
        splitArea: {
          show: true
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 50,
          end: 100
        },
        {
          show: true,
          type: 'slider',
          top: '90%',
          start: 50,
          end: 100
        }
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: [],
          itemStyle: {
            color: '#ec0000',
            color0: '#00da3c',
            borderColor: '#8A0000',
            borderColor0: '#008F28'
          }
        }
      ]
    }

    candlestickChart.setOption(candlestickOption)
  }

  // 初始化成交量图
  if (showIndicators.value) {
    const volumeContainer = document.getElementById('volume-chart')
    if (volumeContainer && !volumeChart) {
      volumeChart = echarts.init(volumeContainer)

      const volumeOption = {
        title: {
          text: '成交量',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '15%'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '成交量',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#1890ff'
            }
          }
        ]
      }

      volumeChart.setOption(volumeOption)
    }

    // 初始化RSI图
    const rsiContainer = document.getElementById('rsi-chart')
    if (rsiContainer && !rsiChart) {
      rsiChart = echarts.init(rsiContainer)

      const rsiOption = {
        title: {
          text: 'RSI指标',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '15%'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          axisLine: {
            lineStyle: {
              color: '#999'
            }
          }
        },
        series: [
          {
            name: 'RSI',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#ff7300'
            }
          }
        ]
      }

      rsiChart.setOption(rsiOption)
    }
  }
}

// 获取K线数据
const loadChartData = async () => {
  loadingChart.value = true
  try {
    console.log('正在获取K线数据...', {
      instId: selectedSymbol.value,
      bar: selectedTimeframe.value,
      limit: 100
    })

    const response = await axios.get(`/api/okx/market/candles`, {
      params: {
        instId: selectedSymbol.value,
        bar: selectedTimeframe.value,
        limit: 100
      }
    })

    console.log('K线数据响应:', response.data)

    if (response.data.code === '0' && response.data.data) {
      chartData.value = response.data.data
      console.log('获取到K线数据:', chartData.value.length, '条')
      calculateTechnicalIndicators()
      updateCharts()
    } else {
      console.warn('K线数据格式错误:', response.data)
      generateMockData()
    }
  } catch (error) {
    console.error('获取K线数据失败:', error)
    message.error('获取K线数据失败: ' + error.message)
    // 使用模拟数据
    generateMockData()
  } finally {
    loadingChart.value = false
  }
}

// 获取实时价格
const loadCurrentPrice = async () => {
  try {
    const response = await axios.get(`/api/okx/market/ticker?instId=${selectedSymbol.value}`)
    if (response.data.code === 0) {
      currentPrice.value = response.data.data[0]
    }
  } catch (error) {
    console.warn('获取实时价格失败:', error.message)
    // 使用模拟数据
    currentPrice.value = {
      instId: selectedSymbol.value,
      last: '50000.00',
      sodUtc8: '2.5',
      high24h: '51000.00',
      low24h: '49000.00',
      vol24h: '1234567'
    }
  }
}

// 生成模拟数据
const generateMockData = () => {
  const mockData = []
  let basePrice = 50000

  for (let i = 0; i < 100; i++) {
    const timestamp = Date.now() - (100 - i) * 60 * 60 * 1000
    const open = basePrice + (Math.random() - 0.5) * 1000
    const close = open + (Math.random() - 0.5) * 500
    const high = Math.max(open, close) + Math.random() * 200
    const low = Math.min(open, close) - Math.random() * 200
    const volume = Math.random() * 1000000

    mockData.push([timestamp, open, high, low, close, volume])
    basePrice = close
  }

  chartData.value = mockData
  calculateTechnicalIndicators()
}

// 计算技术指标
const calculateTechnicalIndicators = () => {
  if (chartData.value.length === 0) return

  const closes = chartData.value.map(item => parseFloat(item[4]))
  const volumes = chartData.value.map(item => parseFloat(item[5]))

  // 计算RSI
  technicalIndicators.value.rsi = calculateRSI(closes, 14)

  // 计算移动平均线
  technicalIndicators.value.ma20 = calculateMA(closes, 20)
  technicalIndicators.value.ma50 = calculateMA(closes, 50)

  // 计算MACD
  technicalIndicators.value.macd = calculateMACD(closes)

  // 计算布林带
  const bb = calculateBollingerBands(closes, 20)
  technicalIndicators.value.bbUpper = bb.upper
  technicalIndicators.value.bbLower = bb.lower

  // 计算市场情绪
  calculateMarketSentiment()
}

// RSI计算
const calculateRSI = (prices, period) => {
  if (prices.length < period + 1) return 50

  let gains = 0
  let losses = 0

  for (let i = 1; i <= period; i++) {
    const change = prices[prices.length - i] - prices[prices.length - i - 1]
    if (change > 0) gains += change
    else losses -= change
  }

  const avgGain = gains / period
  const avgLoss = losses / period
  const rs = avgGain / avgLoss

  return 100 - (100 / (1 + rs))
}

// 移动平均线计算
const calculateMA = (prices, period) => {
  if (prices.length < period) return 0

  const sum = prices.slice(-period).reduce((a, b) => a + b, 0)
  return sum / period
}

// MACD计算（简化版）
const calculateMACD = (prices) => {
  if (prices.length < 26) return 0

  const ema12 = calculateEMA(prices, 12)
  const ema26 = calculateEMA(prices, 26)

  return ema12 - ema26
}

// EMA计算
const calculateEMA = (prices, period) => {
  if (prices.length < period) return 0

  const multiplier = 2 / (period + 1)
  let ema = prices[0]

  for (let i = 1; i < prices.length; i++) {
    ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
  }

  return ema
}

// 布林带计算
const calculateBollingerBands = (prices, period) => {
  if (prices.length < period) return { upper: 0, lower: 0 }

  const ma = calculateMA(prices, period)
  const recentPrices = prices.slice(-period)

  const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - ma, 2), 0) / period
  const stdDev = Math.sqrt(variance)

  return {
    upper: ma + (stdDev * 2),
    lower: ma - (stdDev * 2)
  }
}

// 计算市场情绪
const calculateMarketSentiment = () => {
  const rsi = technicalIndicators.value.rsi
  const currentPrice = parseFloat(chartData.value[chartData.value.length - 1][4])
  const ma20 = technicalIndicators.value.ma20

  let score = 50 // 基础分数

  // RSI影响
  if (rsi > 70) score -= 20
  else if (rsi < 30) score += 20
  else if (rsi > 50) score += 10
  else score -= 10

  // 价格相对于MA20的位置
  if (currentPrice > ma20) score += 15
  else score -= 15

  // MACD影响
  if (technicalIndicators.value.macd > 0) score += 10
  else score -= 10

  score = Math.max(0, Math.min(100, score))

  let label = '中性'
  if (score > 70) label = '极度贪婪'
  else if (score > 60) label = '贪婪'
  else if (score > 40) label = '中性'
  else if (score > 30) label = '恐惧'
  else label = '极度恐惧'

  marketSentiment.value = { score, label }
}

// 生成交易信号
const generateTradingSignals = () => {
  const signals = []
  const currentTime = new Date()

  // 基于技术指标生成信号
  if (technicalIndicators.value.rsi < 30) {
    signals.push({
      time: currentTime.toLocaleTimeString(),
      signal: 'BUY',
      price: currentPrice.value?.last || '50000',
      strength: 0.8,
      description: 'RSI超卖信号'
    })
  }

  if (technicalIndicators.value.rsi > 70) {
    signals.push({
      time: currentTime.toLocaleTimeString(),
      signal: 'SELL',
      price: currentPrice.value?.last || '50000',
      strength: 0.7,
      description: 'RSI超买信号'
    })
  }

  if (technicalIndicators.value.macd > 0) {
    signals.push({
      time: currentTime.toLocaleTimeString(),
      signal: 'BUY',
      price: currentPrice.value?.last || '50000',
      strength: 0.6,
      description: 'MACD金叉信号'
    })
  }

  tradingSignals.value = signals
}

// 更新图表
const updateCharts = () => {
  if (chartData.value.length === 0) return

  // 准备数据
  const dates = []
  const candleData = []
  const volumeData = []
  const rsiData = []

  chartData.value.forEach((item, index) => {
    const timestamp = parseInt(item[0])
    const date = new Date(timestamp).toLocaleTimeString()
    const open = parseFloat(item[1])
    const high = parseFloat(item[2])
    const low = parseFloat(item[3])
    const close = parseFloat(item[4])
    const volume = parseFloat(item[5])

    dates.push(date)
    candleData.push([open, close, low, high])
    volumeData.push(volume)

    // 计算RSI（简化版）
    if (index >= 14) {
      const closes = chartData.value.slice(index - 13, index + 1).map(d => parseFloat(d[4]))
      const rsi = calculateRSI(closes, 14)
      rsiData.push(rsi)
    } else {
      rsiData.push(50)
    }
  })

  // 更新K线图
  if (candlestickChart) {
    candlestickChart.setOption({
      xAxis: {
        data: dates
      },
      series: [{
        data: candleData
      }]
    })
  }

  // 更新成交量图
  if (volumeChart && showIndicators.value) {
    volumeChart.setOption({
      xAxis: {
        data: dates
      },
      series: [{
        data: volumeData
      }]
    })
  }

  // 更新RSI图
  if (rsiChart && showIndicators.value) {
    rsiChart.setOption({
      xAxis: {
        data: dates
      },
      series: [{
        data: rsiData
      }]
    })
  }

  generateTradingSignals()
}

// 刷新图表
const refreshChart = () => {
  loadChartData()
  loadCurrentPrice()
}

// 切换指标显示
const toggleIndicators = () => {
  showIndicators.value = !showIndicators.value

  if (showIndicators.value) {
    // 显示指标时初始化图表
    setTimeout(() => {
      initCharts()
      updateCharts()
    }, 100)
  } else {
    // 隐藏指标时销毁图表
    if (volumeChart) {
      volumeChart.dispose()
      volumeChart = null
    }
    if (rsiChart) {
      rsiChart.dispose()
      rsiChart = null
    }
  }
}

// 交易对变化
const onSymbolChange = () => {
  refreshChart()
}

// 时间周期变化
const onTimeframeChange = () => {
  loadChartData()
}

// 启动实时价格更新
const startPriceUpdates = () => {
  priceUpdateInterval = setInterval(() => {
    loadCurrentPrice()
  }, 5000) // 每5秒更新一次
}

// 停止实时价格更新
const stopPriceUpdates = () => {
  if (priceUpdateInterval) {
    clearInterval(priceUpdateInterval)
    priceUpdateInterval = null
  }
}

// 页面加载时初始化
onMounted(async () => {
  await initCharts()
  await loadChartData()
  loadCurrentPrice()
  startPriceUpdates()
})

// 页面卸载时清理
onUnmounted(() => {
  stopPriceUpdates()

  // 销毁所有图表实例
  if (candlestickChart) {
    candlestickChart.dispose()
    candlestickChart = null
  }
  if (volumeChart) {
    volumeChart.dispose()
    volumeChart = null
  }
  if (rsiChart) {
    rsiChart.dispose()
    rsiChart = null
  }
})
</script>

<style scoped>
.advanced-charts {
  padding: 24px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

#candlestick-chart,
#volume-chart,
#rsi-chart {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}
</style>
