# OKX量化交易系统-系统设计

## 架构总览
- 前端：Vue3 + Vite + Ant Design Vue，SPA单页应用，组件化开发
- 后端：FastAPI，RESTful API，模块化设计，WebSocket实时推送
- 数据库：SQLite（可扩展MySQL/PostgreSQL），SQLAlchemy ORM
- AI分析：可对接大模型/AI服务
- 交易对接：OKX官方API（REST+WebSocket）

## 主要模块
1. 用户与权限模块
2. 策略管理模块
3. 订单管理模块
4. 账户与资产模块
5. AI分析模块
6. 统计看板模块
7. 公共与安全模块
8. OKX API集成模块（REST+WebSocket）

## 前后端交互流程
- 用户注册/登录 -> 获取token -> 访问受限API
- 策略创建/编辑 -> 策略回测/实盘 -> 订单生成
- 订单管理 -> 下单/撤单/历史查询
- 账户资产 -> 查询余额/持仓/划转/杠杆
- AI分析 -> 生成分析报告/辅助决策
- 统计看板 -> 数据可视化
- OKX WebSocket -> 实时推送行情、账户、持仓、订单

## 权限与安全设计
- JWT鉴权，接口权限控制，token过期自动跳转登录
- 用户表含api_key、api_secret、passphrase，后端自动读取签名
- 密码加密存储（bcrypt）
- 重要操作日志与风控
- 前端路由守卫，动态菜单，权限不足跳转403

## WebSocket推送
- 行情推送：/ws/okx/market，公开频道
- 账户/持仓/订单推送：/ws/okx/private，需token，自动签名登录OKX官方WebSocket
- 支持多频道订阅，前端实时刷新

## 自动化与文档
- 接口文档、数据结构文档自动同步
- 代码变更自动补全文档
- 支持API Mock与自动化测试

## 扩展性与可维护性
- 支持多交易所、多币种扩展
- 策略与AI模块可插件化
- 前后端均可二次开发
- 代码结构清晰，文档齐全，便于团队协作

## 典型流程图
1. 用户登录 -> 获取token -> 访问受限API
2. 下单流程：前端表单 -> REST下单接口 -> OKX官方API -> WebSocket推送订单状态
3. 持仓/账户/杠杆/划转：前端表单 -> REST接口 -> OKX官方API -> WebSocket推送实时数据
