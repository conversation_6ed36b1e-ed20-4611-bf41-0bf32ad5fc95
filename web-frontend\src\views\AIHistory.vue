<template>
  <div class="ai-history">
    <h2>AI分析历史</h2>

    <!-- 筛选和统计 -->
    <a-row :gutter="16" style="margin-bottom: 24px;">
      <a-col :span="16">
        <a-form layout="inline">
          <a-form-item label="分析类型">
            <a-select v-model="filter.type" style="width: 150px" placeholder="全部类型" allowClear>
              <a-select-option value="price_prediction">价格预测</a-select-option>
              <a-select-option value="trading_signal">交易信号</a-select-option>
              <a-select-option value="risk_analysis">风险分析</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="loadHistory">筛选</a-button>
          </a-form-item>
        </a-form>
      </a-col>
      <a-col :span="8" style="text-align: right;">
        <a-statistic title="总分析次数" :value="stats.total_analyses" />
      </a-col>
    </a-row>

    <!-- 历史记录列表 -->
    <a-card title="分析历史" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="history"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'suggestion'">
            <span style="max-width: 200px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              {{ record.suggestion }}
            </span>
          </template>
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">查看详情</a-button>
              <a-button type="link" size="small" @click="deleteRecord(record)">删除</a-button>
            </a-space>
          </template>
          <template v-else>
            {{ record[column.dataIndex] }}
          </template>
        </template>
        <template #empty>
          <div style="text-align:center; padding:32px;">暂无分析历史</div>
        </template>
      </a-table>
    </a-card>

    <!-- 详情弹窗 -->
    <a-modal
      :open="detailVisible"
      @cancel="detailVisible = false"
      title="分析详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentDetail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="分析类型">
            <a-tag :color="getTypeColor(currentDetail.type)">
              {{ getTypeText(currentDetail.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDate(currentDetail.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="建议" :span="2">
            {{ currentDetail.suggestion }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>详细内容</a-divider>

        <div v-if="currentDetail.type === 'price_prediction' && currentDetail.content">
          <h4>预测结果</h4>
          <a-row :gutter="16" style="margin-bottom: 16px;">
            <a-col :span="8">
              <a-statistic title="交易对" :value="currentDetail.content.symbol" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="趋势" :value="currentDetail.content.trend === 'bullish' ? '看涨' : '看跌'" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="置信度" :value="currentDetail.content.confidence" suffix="%" />
            </a-col>
          </a-row>

          <h4>预测详情</h4>
          <a-table
            :columns="predictionColumns"
            :data-source="currentDetail.content.predictions"
            :pagination="false"
            size="small"
          />
        </div>

        <div v-if="currentDetail.type === 'trading_signal' && currentDetail.content">
          <h4>信号详情</h4>
          <a-row :gutter="16" style="margin-bottom: 16px;">
            <a-col :span="6">
              <a-statistic title="交易对" :value="currentDetail.content.symbol" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="信号" :value="getSignalText(currentDetail.content.signal)" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="策略" :value="getStrategyText(currentDetail.content.strategy)" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="置信度" :value="currentDetail.content.confidence" suffix="%" />
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic title="目标价格" :value="currentDetail.content.target_price" suffix="USDT" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="止损价格" :value="currentDetail.content.stop_loss" suffix="USDT" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="止盈价格" :value="currentDetail.content.take_profit" suffix="USDT" />
            </a-col>
          </a-row>

          <a-divider>信号原因</a-divider>
          <p>{{ currentDetail.content.reason }}</p>
        </div>

        <div v-if="currentDetail.type === 'risk_analysis' && currentDetail.content">
          <h4>风险分析</h4>
          <a-row :gutter="16" style="margin-bottom: 16px;">
            <a-col :span="6">
              <a-statistic title="交易对" :value="currentDetail.content.symbol" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="风险等级" :value="getRiskText(currentDetail.content.risk_level)" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="风险评分" :value="currentDetail.content.risk_score" suffix="/100" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="波动率" :value="currentDetail.content.volatility" suffix="%" />
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-statistic title="95% VaR" :value="currentDetail.content.var_95" suffix="USDT" />
            </a-col>
            <a-col :span="12">
              <a-statistic title="最大损失" :value="currentDetail.content.max_loss" suffix="USDT" />
            </a-col>
          </a-row>

          <a-divider>风险建议</a-divider>
          <p>{{ currentDetail.content.recommendation }}</p>
        </div>

        <!-- 原始数据 -->
        <a-divider>原始数据</a-divider>
        <a-input.TextArea
          :value="JSON.stringify(currentDetail.content, null, 2)"
          :rows="8"
          readonly
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'

const filter = ref({
  type: null
})

const history = ref([])
const stats = ref({ total_analyses: 0 })
const loading = ref(false)
const detailVisible = ref(false)
const currentDetail = ref(null)

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

const columns = [
  { title: '分析类型', dataIndex: 'type', key: 'type' },
  { title: '建议', dataIndex: 'suggestion', key: 'suggestion' },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at' },
  { title: '操作', key: 'action' }
]

const predictionColumns = [
  { title: '日期', dataIndex: 'date', key: 'date' },
  { title: '预测价格', dataIndex: 'price', key: 'price' },
  { title: '置信度', dataIndex: 'confidence', key: 'confidence' }
]

function loadHistory() {
  loading.value = true

  const params = new URLSearchParams({
    limit: pagination.value.pageSize,
    offset: (pagination.value.current - 1) * pagination.value.pageSize
  })

  if (filter.value.type) {
    params.append('type', filter.value.type)
  }

  fetch('/api/ai/history?' + params.toString(), {
    headers: {
      'Authorization': 'Bearer ' + localStorage.getItem('token')
    }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code === '0') {
        history.value = res.data
        pagination.value.total = res.data.length
      } else {
        message.error(res.msg || '获取历史记录失败')
      }
    })
    .catch(() => {
      message.error('网络异常')
    })
    .finally(() => {
      loading.value = false
    })
}

function loadStats() {
  fetch('/api/ai/stats', {
    headers: {
      'Authorization': 'Bearer ' + localStorage.getItem('token')
    }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code === '0') {
        stats.value = res.data
      }
    })
    .catch(() => {
      // 忽略统计加载错误
    })
}

function handleTableChange(pagination) {
  pagination.value.current = pagination.current
  pagination.value.pageSize = pagination.pageSize
  loadHistory()
}

function viewDetail(record) {
  currentDetail.value = record
  detailVisible.value = true
}

function deleteRecord(record) {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条分析记录吗？',
    onOk: () => {
      // 这里可以调用删除API
      message.success('删除成功')
      loadHistory()
    }
  })
}

function getTypeColor(type) {
  const colorMap = {
    'price_prediction': 'blue',
    'trading_signal': 'green',
    'risk_analysis': 'orange'
  }
  return colorMap[type] || 'default'
}

function getTypeText(type) {
  const textMap = {
    'price_prediction': '价格预测',
    'trading_signal': '交易信号',
    'risk_analysis': '风险分析'
  }
  return textMap[type] || type
}

function getSignalText(signal) {
  const signalMap = {
    'buy': '买入',
    'sell': '卖出',
    'hold': '持有'
  }
  return signalMap[signal] || signal
}

function getStrategyText(strategy) {
  const strategyMap = {
    'trend_following': '趋势跟随',
    'mean_reversion': '均值回归',
    'breakout': '突破策略',
    'momentum': '动量策略'
  }
  return strategyMap[strategy] || strategy
}

function getRiskText(risk) {
  const riskMap = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return riskMap[risk] || risk
}

function formatDate(date) {
  const dateObj = new Date(date)
  return dateObj.toLocaleString('zh-CN')
}

onMounted(() => {
  loadHistory()
  loadStats()
})
</script>

<style scoped>
.ai-history {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}
</style>