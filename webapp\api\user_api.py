from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session
from ..models import User
from ..db import get_db
from passlib.context import CryptContext
from jose import jwt, JWTError
import datetime
from fastapi.security import OAuth2PasswordBearer
from passlib.hash import bcrypt

router = APIRouter()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
SECRET_KEY = "your_secret_key"  # 建议放到配置文件
ALGORITHM = "HS256"
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/user/login")


class UserCreate(BaseModel):
    username: str
    password: str


class UserLogin(BaseModel):
    username: str
    password: str


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: int = 3600):
    to_encode = data.copy()
    expire = datetime.datetime.utcnow() + datetime.timedelta(seconds=expires_delta)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)


def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        user_id: int = payload.get("user_id")
        if username is None or user_id is None:
            raise HTTPException(status_code=401, detail="无效token")
        return {"username": username, "user_id": user_id}
    except JWTError:
        raise HTTPException(status_code=401, detail="无效token")


@router.post("/register")
def register(user: UserCreate, db: Session = Depends(get_db)):
    if db.query(User).filter(User.username == user.username).first():
        raise HTTPException(status_code=400, detail="用户名已存在")
    db_user = User(
        username=user.username,
        password=bcrypt.hash(user.password)
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return {"code": 0, "msg": "注册成功"}


@router.post("/login")
def login(user: UserLogin, db: Session = Depends(get_db)):
    db_user = db.query(User).filter(User.username == user.username).first()
    if not db_user or not bcrypt.verify(user.password, db_user.password):
        raise HTTPException(status_code=400, detail="用户名或密码错误")

    expires_delta = 3600  # 1小时
    token = create_access_token(
        {"sub": db_user.username, "user_id": db_user.id}, expires_delta)

    # 计算过期时间戳
    exp = int((datetime.datetime.utcnow() + datetime.timedelta(seconds=expires_delta)).timestamp())

    return {
        "code": 0,
        "msg": "登录成功",
        "token": token,
        "exp": exp
    }


@router.get("/me")
def get_me(current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    db_user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    return {
        "code": 0,
        "msg": "success",
        "data": {
            "id": db_user.id,
            "username": db_user.username,
            "role": getattr(db_user, "role", "user"),
            "avatar": getattr(db_user, "avatar", "")
        }
    }


@router.get("/list")
def list_users(db: Session = Depends(get_db)):
    users = db.query(User).all()
    return {"code": 0, "msg": "success", "data": [{"id": u.id, "username": u.username} for u in users]}


@router.get("/stats")
def user_stats():
    return {"code": 0, "msg": "success", "data": {"user_count": 1, "strategy_count": 0, "order_count": 0}}
