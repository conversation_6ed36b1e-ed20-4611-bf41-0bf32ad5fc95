from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session
from ..models import User
from ..db import get_db
from passlib.context import CryptContext
from jose import jwt, JWTError
import datetime
from fastapi.security import OAuth2PasswordBearer
from passlib.hash import bcrypt
# import requests
# import hmac
# import hashlib
# import base64
# import time
# from cryptography.fernet import Fernet

router = APIRouter()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
SECRET_KEY = "your_secret_key"  # 建议放到配置文件
ALGORITHM = "HS256"
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/user/login")

# 用于加密API密钥的密钥（实际使用中应该放在环境变量中）
# 使用固定密钥以确保数据可以正确解密
# ENCRYPTION_KEY = b'ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg='  # 固定密钥，实际使用中应从环境变量获取
# try:
#     cipher_suite = Fernet(ENCRYPTION_KEY)
# except Exception:
#     # 如果密钥有问题，生成新的密钥
#     ENCRYPTION_KEY = Fernet.generate_key()
#     cipher_suite = Fernet(ENCRYPTION_KEY)
#     print(f"Generated new encryption key: {ENCRYPTION_KEY.decode()}")


class UserCreate(BaseModel):
    username: str
    password: str


class UserLogin(BaseModel):
    username: str
    password: str


class APIConfig(BaseModel):
    api_key: str
    api_secret: str
    passphrase: str
    environment: str = "demo"


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: int = 3600):
    to_encode = data.copy()
    expire = datetime.datetime.utcnow() + datetime.timedelta(seconds=expires_delta)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)


def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        user_id: int = payload.get("user_id")
        if username is None or user_id is None:
            raise HTTPException(status_code=401, detail="无效token")
        return {"username": username, "user_id": user_id}
    except JWTError:
        raise HTTPException(status_code=401, detail="无效token")


@router.post("/register")
def register(user: UserCreate, db: Session = Depends(get_db)):
    if db.query(User).filter(User.username == user.username).first():
        raise HTTPException(status_code=400, detail="用户名已存在")
    db_user = User(
        username=user.username,
        password=bcrypt.hash(user.password)
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return {"code": 0, "msg": "注册成功"}


@router.post("/login")
def login(user: UserLogin, db: Session = Depends(get_db)):
    db_user = db.query(User).filter(User.username == user.username).first()
    if not db_user or not bcrypt.verify(user.password, db_user.password):
        raise HTTPException(status_code=400, detail="用户名或密码错误")

    expires_delta = 3600  # 1小时
    token = create_access_token(
        {"sub": db_user.username, "user_id": db_user.id}, expires_delta)

    # 计算过期时间戳
    exp = int((datetime.datetime.utcnow() + datetime.timedelta(seconds=expires_delta)).timestamp())

    return {
        "code": 0,
        "msg": "登录成功",
        "token": token,
        "exp": exp
    }


@router.get("/me")
def get_me(current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    db_user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    return {
        "code": 0,
        "msg": "success",
        "data": {
            "id": db_user.id,
            "username": db_user.username,
            "role": getattr(db_user, "role", "user"),
            "avatar": getattr(db_user, "avatar", "")
        }
    }


@router.get("/list")
def list_users(db: Session = Depends(get_db)):
    users = db.query(User).all()
    return {"code": 0, "msg": "success", "data": [{"id": u.id, "username": u.username} for u in users]}


@router.get("/stats")
def user_stats():
    return {"code": 0, "msg": "success", "data": {"user_count": 1, "strategy_count": 0, "order_count": 0}}


# API密钥管理功能

def encrypt_api_secret(secret: str) -> str:
    """加密API密钥（暂时禁用）"""
    return secret  # 暂时不加密


def decrypt_api_secret(encrypted_secret: str) -> str:
    """解密API密钥（暂时禁用）"""
    return encrypted_secret  # 暂时不解密


def okx_sign(api_secret: str, method: str, path: str, body: str):
    """生成OKX签名（暂时禁用）"""
    # 暂时返回模拟数据
    import time
    timestamp = str(int(time.time()))
    return timestamp, "mock_signature"


@router.post("/api-config")
def save_api_config(
    config: APIConfig,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """保存API配置"""
    try:
        # 加密敏感信息
        encrypted_secret = encrypt_api_secret(config.api_secret)
        encrypted_passphrase = encrypt_api_secret(config.passphrase)

        # 更新用户记录
        db_user = db.query(User).filter(User.id == current_user["user_id"]).first()
        if not db_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 更新用户的API配置
        db_user.api_key = config.api_key
        db_user.api_secret = encrypted_secret
        db_user.passphrase = encrypted_passphrase
        # db_user.environment = config.environment  # 暂时注释

        db.commit()

        return {
            "code": 0,
            "msg": "API配置保存成功",
            "data": None
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"保存失败: {str(e)}",
            "data": None
        }


@router.get("/api-config")
def get_api_config(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取API配置（不返回敏感信息）"""
    try:
        db_user = db.query(User).filter(User.id == current_user["user_id"]).first()
        if not db_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        return {
            "code": 0,
            "msg": "获取成功",
            "data": {
                "api_key": getattr(db_user, "api_key", ""),
                "environment": "demo",  # 暂时固定为demo
                "has_config": bool(getattr(db_user, "api_key", ""))
            }
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"获取失败: {str(e)}",
            "data": None
        }


@router.delete("/api-config")
def delete_api_config(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """清除API配置"""
    try:
        db_user = db.query(User).filter(User.id == current_user["user_id"]).first()
        if not db_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 清除API配置
        db_user.api_key = None
        db_user.api_secret = None
        db_user.passphrase = None
        # db_user.environment = "demo"  # 暂时注释

        db.commit()

        return {
            "code": 0,
            "msg": "API配置已清除",
            "data": None
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"清除失败: {str(e)}",
            "data": None
        }


@router.post("/test-api")
def test_api_connection(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """测试API连接"""
    try:
        db_user = db.query(User).filter(User.id == current_user["user_id"]).first()
        if not db_user or not getattr(db_user, "api_key", ""):
            return {
                "code": 1,
                "msg": "请先配置API密钥",
                "data": None
            }

        # 解密API密钥
        api_key = db_user.api_key
        api_secret = decrypt_api_secret(db_user.api_secret)
        passphrase = decrypt_api_secret(db_user.passphrase)
        environment = "demo"  # 暂时固定为demo

        # 模拟测试连接（暂时不连接真实API）
        return {
            "code": 0,
            "msg": "API连接测试成功（模拟）",
            "data": {
                "accountType": "交易账户",
                "permissions": ["读取", "交易"],
                "environment": environment
            }
        }

    except Exception as e:
        return {
            "code": 1,
            "msg": f"测试失败: {str(e)}",
            "data": None
        }
