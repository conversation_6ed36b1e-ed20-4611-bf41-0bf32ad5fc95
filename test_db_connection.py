#!/usr/bin/env python3
"""
测试数据库连接和用户查询
"""

import sqlite3
from passlib.hash import bcrypt

def test_database():
    try:
        # 连接数据库
        conn = sqlite3.connect('db.sqlite3')
        cursor = conn.cursor()
        
        # 查询用户
        cursor.execute('SELECT id, username, password FROM user WHERE username = ?', ('string',))
        user = cursor.fetchone()
        
        if user:
            user_id, username, stored_password = user
            print(f"找到用户: ID={user_id}, Username={username}")
            print(f"存储的密码哈希: {stored_password[:50]}...")
            
            # 测试密码验证
            test_password = "string"
            is_valid = bcrypt.verify(test_password, stored_password)
            print(f"密码验证结果: {is_valid}")
            
            if is_valid:
                print("✅ 数据库和密码验证正常")
            else:
                print("❌ 密码验证失败")
        else:
            print("❌ 用户不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

def test_user_api_import():
    try:
        from webapp.api.user_api import get_current_user, verify_password
        print("✅ User API导入成功")
        
        # 测试密码验证函数
        test_hash = bcrypt.hash("string")
        result = verify_password("string", test_hash)
        print(f"密码验证函数测试: {result}")
        
    except Exception as e:
        print(f"❌ User API导入失败: {e}")

if __name__ == "__main__":
    print("=== 测试数据库连接 ===")
    test_database()
    
    print("\n=== 测试User API导入 ===")
    test_user_api_import()
