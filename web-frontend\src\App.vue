<script setup>
import NavBar from './components/NavBar.vue'
import { onMounted } from 'vue'
import axios from 'axios'
import { useUserStore } from './stores/user'
const userStore = useUserStore()
onMounted(async () => {
  const token = localStorage.getItem('token')
  const tokenExp = localStorage.getItem('token_exp')

  // 检查token是否存在且未过期
  if (token && tokenExp) {
    const now = Math.floor(Date.now() / 1000)
    const exp = Number(tokenExp)

    if (exp > now) {
      try {
        const res = await axios.get('/api/user/me')
        if (res.data.code === 0) {
          userStore.setUserInfo(res.data.data)
        }
      } catch (e) {
        // token 失效，清除本地存储
        localStorage.removeItem('token')
        localStorage.removeItem('token_exp')
      }
    } else {
      // token已过期，清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('token_exp')
    }
  }
})
</script>

<template>
  <NavBar />
  <router-view />
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
