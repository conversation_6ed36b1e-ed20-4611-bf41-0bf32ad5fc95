#!/usr/bin/env python3
"""
创建测试用户
"""

import sqlite3
from passlib.hash import bcrypt

def create_test_user():
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    # 删除现有的测试用户
    cursor.execute('DELETE FROM user WHERE username = ?', ('admin',))
    
    # 创建新的测试用户
    username = 'admin'
    password = 'admin'
    hashed_password = bcrypt.hash(password)
    
    cursor.execute('''
        INSERT INTO user (username, password, environment, is_active)
        VALUES (?, ?, ?, ?)
    ''', (username, hashed_password, 'demo', True))
    
    conn.commit()
    conn.close()
    
    print(f"创建测试用户成功:")
    print(f"  用户名: {username}")
    print(f"  密码: {password}")
    print(f"  密码哈希: {hashed_password}")

if __name__ == "__main__":
    create_test_user()
