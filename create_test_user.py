#!/usr/bin/env python3
"""
创建测试用户
"""

import sqlite3
from passlib.hash import bcrypt

def create_test_user():
    conn = sqlite3.connect('okx_trading.db')
    cursor = conn.cursor()
    
    # 删除现有的测试用户
    cursor.execute('DELETE FROM users WHERE username = ?', ('admin',))
    
    # 创建新的测试用户
    username = 'admin'
    password = 'admin'
    hashed_password = bcrypt.hash(password)
    
    cursor.execute('''
        INSERT INTO users (username, email, hashed_password, is_active)
        VALUES (?, ?, ?, ?)
    ''', (username, '<EMAIL>', hashed_password, True))
    
    conn.commit()
    conn.close()
    
    print(f"创建测试用户成功:")
    print(f"  用户名: {username}")
    print(f"  密码: {password}")
    print(f"  密码哈希: {hashed_password}")

if __name__ == "__main__":
    create_test_user()
