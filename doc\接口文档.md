# OKX量化交易系统-接口文档

## OKX相关REST接口

### 获取行情
- 路径：`GET /api/okx/market/ticker?instId=BTC-USDT`
- 参数：instId（产品ID）
- 返回：{ code, msg, data }
- 权限：公开

### 获取账户余额
- 路径：`GET /api/okx/account/balance`
- Header：Authorization: Bearer <token>
- 返回：{ code, msg, data }
- 权限：需登录

### 下单
- 路径：`POST /api/okx/order/create`
- Header：Authorization: Bearer <token>
- 参数：{ instId, tdMode, side, ordType, px, sz, ... }
- 返回：{ code, msg, data }
- 权限：需登录

### 撤单
- 路径：`POST /api/okx/order/cancel`
- Header：Authorization: Bearer <token>
- 参数：{ instId, ordId/clOrdId }
- 返回：{ code, msg, data }
- 权限：需登录

### 查询订单
- 路径：`GET /api/okx/order/list`
- Header：Authorization: Bearer <token>
- 返回：{ code, msg, data }
- 权限：需登录

### 查询杠杆倍数
- 路径：`GET /api/okx/leverage-info?instId=BTC-USDT`
- Header：Authorization: Bearer <token>
- 参数：instId, mgnMode, ccy
- 返回：{ code, msg, data }
- 权限：需登录

### 设置杠杆倍数
- 路径：`POST /api/okx/set-leverage`
- Header：Authorization: Bearer <token>
- 参数：{ lever, mgnMode, instId/ccy }
- 返回：{ code, msg, data }
- 权限：需登录

### 资金划转
- 路径：`POST /api/okx/transfer`
- Header：Authorization: Bearer <token>
- 参数：{ ccy, amt, from, to, type, subAcct }
- 返回：{ code, msg, data }
- 权限：需登录

### 查询合约持仓
- 路径：`GET /api/okx/positions?instType=SWAP`
- Header：Authorization: Bearer <token>
- 参数：instType, instId, posId
- 返回：{ code, msg, data }
- 权限：需登录

---

## OKX WebSocket接口

### 行情推送
- 路径：`ws://<host>/ws/okx/market`
- 说明：推送BTC-USDT等行情数据
- 权限：公开

### 账户/持仓/订单推送
- 路径：`ws://<host>/ws/okx/private?token=<token>`
- 连接后发送：
  ```json
  { "channels": [ { "channel": "account" }, { "channel": "positions" }, { "channel": "orders" } ] }
  ```
- 推送：OKX官方原始数据
- 权限：需登录

---

## 统一返回结构
- 所有接口返回：`{ code: "0/1", msg: "", data: ... }`
- code=0为成功，1为失败，msg为提示信息

## 用户相关
### 注册
- 路径：`POST /api/user/register`
- 参数：`