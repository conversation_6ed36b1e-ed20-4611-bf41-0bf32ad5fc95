#!/usr/bin/env python3
"""
测试所有API端点
"""

import requests
import json

def test_all_apis():
    print("=== 测试所有API端点 ===")
    
    # 1. 测试登录
    login_url = "http://localhost:8002/api/user/login"
    login_data = {"username": "admin", "password": "admin"}
    
    try:
        print("1. 测试登录...")
        login_response = requests.post(login_url, json=login_data)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            if login_result.get("code") == 0:
                token = login_result.get("token")
                print(f"   ✅ 登录成功")
                headers = {"Authorization": f"Bearer {token}"}
                
                # 2. 测试K线数据
                print("\n2. 测试K线数据...")
                candles_url = "http://localhost:8002/api/okx/market/candles?instId=BTC-USDT-SWAP&bar=1H&limit=10"
                candles_response = requests.get(candles_url)
                print(f"   K线数据状态: {candles_response.status_code}")
                if candles_response.status_code == 200:
                    print("   ✅ K线数据获取成功")
                else:
                    print(f"   ❌ K线数据获取失败: {candles_response.text}")
                
                # 3. 测试持仓查询
                print("\n3. 测试持仓查询...")
                positions_url = "http://localhost:8002/api/okx/positions"
                positions_response = requests.get(positions_url, headers=headers)
                print(f"   持仓查询状态: {positions_response.status_code}")
                if positions_response.status_code == 200:
                    print("   ✅ 持仓查询成功")
                else:
                    print(f"   ❌ 持仓查询失败: {positions_response.text}")
                
                # 4. 测试风险设置
                print("\n4. 测试风险设置...")
                risk_url = "http://localhost:8002/api/user/risk-settings"
                risk_response = requests.get(risk_url, headers=headers)
                print(f"   风险设置状态: {risk_response.status_code}")
                if risk_response.status_code == 200:
                    print("   ✅ 风险设置获取成功")
                else:
                    print(f"   ❌ 风险设置获取失败: {risk_response.text}")
                
                # 5. 测试每日统计
                print("\n5. 测试每日统计...")
                stats_url = "http://localhost:8002/api/user/daily-stats"
                stats_response = requests.get(stats_url, headers=headers)
                print(f"   每日统计状态: {stats_response.status_code}")
                if stats_response.status_code == 200:
                    print("   ✅ 每日统计获取成功")
                else:
                    print(f"   ❌ 每日统计获取失败: {stats_response.text}")
                
                # 6. 测试账户余额
                print("\n6. 测试账户余额...")
                balance_url = "http://localhost:8002/api/okx/account/balance"
                balance_response = requests.get(balance_url, headers=headers)
                print(f"   账户余额状态: {balance_response.status_code}")
                if balance_response.status_code == 200:
                    print("   ✅ 账户余额查询成功")
                else:
                    print(f"   ❌ 账户余额查询失败: {balance_response.text}")
                
            else:
                print(f"   ❌ 登录失败: {login_result.get('msg')}")
        else:
            print(f"   ❌ 登录请求失败: {login_response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_all_apis()
