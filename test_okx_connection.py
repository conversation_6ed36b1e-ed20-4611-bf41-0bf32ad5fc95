#!/usr/bin/env python3
"""
测试OKX API连接
"""

import requests
import json

def test_okx_api():
    print("=== 测试OKX API连接 ===")
    
    # 测试登录
    login_url = "http://localhost:8002/api/user/login"
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        print("1. 测试登录...")
        login_response = requests.post(login_url, json=login_data)
        print(f"   登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            if login_result.get("code") == 0:
                token = login_result.get("token")
                print(f"   ✅ 登录成功，Token: {token[:20]}...")
                
                # 测试账户余额
                print("\n2. 测试账户余额查询...")
                balance_url = "http://localhost:8002/api/okx/account/balance"
                headers = {"Authorization": f"Bearer {token}"}
                
                balance_response = requests.get(balance_url, headers=headers)
                print(f"   余额查询状态: {balance_response.status_code}")
                print(f"   余额查询响应: {balance_response.text}")
                
                # 测试持仓查询
                print("\n3. 测试持仓查询...")
                positions_url = "http://localhost:8002/api/okx/account/positions"
                
                positions_response = requests.get(positions_url, headers=headers)
                print(f"   持仓查询状态: {positions_response.status_code}")
                print(f"   持仓查询响应: {positions_response.text}")
                
            else:
                print(f"   ❌ 登录失败: {login_result.get('msg')}")
        else:
            print(f"   ❌ 登录请求失败: {login_response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_okx_api()
