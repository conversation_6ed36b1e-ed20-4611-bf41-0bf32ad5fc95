#!/usr/bin/env python3
"""
测试密码哈希和验证的脚本
"""

import sqlite3
from passlib.hash import bcrypt

def test_password():
    # 连接数据库获取用户信息
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    cursor.execute('SELECT id, username, password FROM user WHERE username = ?', ('string',))
    user = cursor.fetchone()
    conn.close()
    
    if not user:
        print("❌ 用户不存在")
        return
    
    user_id, username, stored_password = user
    print(f"用户ID: {user_id}")
    print(f"用户名: {username}")
    print(f"存储的密码哈希: {stored_password}")
    
    # 测试密码
    test_password = "string"
    print(f"测试密码: {test_password}")
    
    # 验证密码
    try:
        is_valid = bcrypt.verify(test_password, stored_password)
        print(f"密码验证结果: {is_valid}")
        
        if is_valid:
            print("✅ 密码验证成功！")
        else:
            print("❌ 密码验证失败")
            
            # 尝试重新生成哈希进行比较
            new_hash = bcrypt.hash(test_password)
            print(f"新生成的哈希: {new_hash}")
            
            # 验证新哈希
            is_new_valid = bcrypt.verify(test_password, new_hash)
            print(f"新哈希验证结果: {is_new_valid}")
            
    except Exception as e:
        print(f"❌ 密码验证出错: {e}")

if __name__ == "__main__":
    test_password()
