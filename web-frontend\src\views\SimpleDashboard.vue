<template>
  <div class="simple-dashboard">
    <h1>欢迎来到量化交易系统</h1>
    <div class="welcome-content">
      <a-card title="系统概览" style="margin-bottom: 16px;">
        <p>您已成功登录系统！</p>
        <p>当前用户: {{ userStore.username }}</p>
        <p>用户角色: {{ userStore.role }}</p>
      </a-card>
      
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card title="快速导航" hoverable>
            <div class="nav-item">
              <router-link to="/ai/predict">
                <a-button type="primary" block>AI行情预测</a-button>
              </router-link>
            </div>
            <div class="nav-item">
              <router-link to="/okx/positions">
                <a-button block>查看持仓</a-button>
              </router-link>
            </div>
            <div class="nav-item">
              <router-link to="/strategies">
                <a-button block>策略管理</a-button>
              </router-link>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="8">
          <a-card title="系统状态" hoverable>
            <a-statistic
              title="系统运行状态"
              value="正常"
              :value-style="{ color: '#52c41a' }"
            />
            <div style="margin-top: 16px;">
              <a-tag color="green">前端服务正常</a-tag>
              <a-tag color="blue">后端API正常</a-tag>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="8">
          <a-card title="快速操作" hoverable>
            <a-space direction="vertical" style="width: 100%;">
              <a-button type="dashed" block @click="testMessage">
                测试消息
              </a-button>
              <a-button block @click="refreshPage">
                刷新页面
              </a-button>
            </a-space>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from '../stores/user'
import { message } from 'ant-design-vue'

const userStore = useUserStore()

const testMessage = () => {
  message.success('测试消息：系统运行正常！')
}

const refreshPage = () => {
  window.location.reload()
}
</script>

<style scoped>
.simple-dashboard {
  padding: 20px;
}

.welcome-content {
  margin-top: 20px;
}

.nav-item {
  margin-bottom: 12px;
}

.nav-item:last-child {
  margin-bottom: 0;
}
</style>
