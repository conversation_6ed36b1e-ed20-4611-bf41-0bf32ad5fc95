import pytest
import requests
import json
from unittest.mock import patch, MagicMock

# 测试基础配置
BASE_URL = "http://localhost:8000"
TEST_TOKEN = "test_token_123"
TEST_USER_ID = 1


class TestOKXAPI:
    """OKX API自动化测试用例"""

    @pytest.fixture
    def headers(self):
        """测试用请求头"""
        return {
            "Authorization": f"Bearer {TEST_TOKEN}",
            "Content-Type": "application/json"
        }

    @pytest.fixture
    def mock_user_api(self):
        """Mock用户API信息"""
        return {
            "api_key": "test_api_key",
            "api_secret": "test_api_secret",
            "passphrase": "test_passphrase"
        }

    def test_get_market_ticker_success(self):
        """测试获取行情成功"""
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": "0",
                "msg": "",
                "data": [{
                    "instId": "BTC-USDT",
                    "last": "50000",
                    "lastSz": "0.1",
                    "askPx": "50001",
                    "askSz": "1.0",
                    "bidPx": "49999",
                    "bidSz": "1.0",
                    "open24h": "49000",
                    "high24h": "51000",
                    "low24h": "48000",
                    "volCcy24h": "1000000",
                    "vol24h": "20",
                    "ts": "1640995200000"
                }]
            }
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/okx/market/ticker?instId=BTC-USDT")
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == "0"
            assert len(data["data"]) > 0
            assert data["data"][0]["instId"] == "BTC-USDT"

    def test_get_market_ticker_invalid_instid(self):
        """测试获取行情-无效产品ID"""
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 400
            mock_response.text = "Invalid instId"
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/okx/market/ticker?instId=INVALID")
            assert response.status_code == 400

    def test_get_account_balance_success(self, headers, mock_user_api):
        """测试获取账户余额成功"""
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": "0",
                "msg": "",
                "data": [{
                    "ccy": "USDT",
                    "cashBal": "1000.0",
                    "upl": "0",
                    "availBal": "1000.0",
                    "availEq": "1000.0",
                    "ordFrozen": "0",
                    "uplLiab": "0",
                    "crossLiab": "0",
                    "isoLiab": "0",
                    "mgnRatio": "",
                    "interest": "0",
                    "twap": "0",
                    "maxLoan": "",
                    "eqUsd": "1000.0",
                    "notionalLever": "",
                    "stgyEq": "0",
                    "isoUpl": ""
                }]
            }
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/okx/account/balance", headers=headers)
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == "0"
            assert len(data["data"]) > 0

    def test_get_account_balance_no_auth(self):
        """测试获取账户余额-无权限"""
        response = requests.get(f"{BASE_URL}/api/okx/account/balance")
        assert response.status_code == 401

    def test_get_account_balance_no_api_config(self, headers):
        """测试获取账户余额-无API配置"""
        with patch('webapp.api.okx_api.get_user_api') as mock_get_user:
            mock_get_user.side_effect = Exception("请先在账户设置中配置OKX API信息")

            response = requests.get(
                f"{BASE_URL}/api/okx/account/balance", headers=headers)
            assert response.status_code == 400

    def test_create_order_success(self, headers, mock_user_api):
        """测试下单成功"""
        order_data = {
            "instId": "BTC-USDT-SWAP",
            "tdMode": "cross",
            "side": "buy",
            "ordType": "limit",
            "px": "50000",
            "sz": "1"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": "0",
                "msg": "",
                "data": [{
                    "clOrdId": "",
                    "ordId": "*********",
                    "tag": "",
                    "sCode": "0",
                    "sMsg": ""
                }]
            }
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/okx/order/create",
                headers=headers,
                json=order_data
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == "0"
            assert "ordId" in data["data"][0]

    def test_create_order_invalid_params(self, headers):
        """测试下单-无效参数"""
        invalid_order = {
            "instId": "BTC-USDT-SWAP",
            "tdMode": "cross",
            "side": "buy",
            "ordType": "limit",
            # 缺少必要参数
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 400
            mock_response.text = "Invalid parameters"
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/okx/order/create",
                headers=headers,
                json=invalid_order
            )
            assert response.status_code == 400

    def test_cancel_order_success(self, headers, mock_user_api):
        """测试撤单成功"""
        cancel_data = {
            "instId": "BTC-USDT-SWAP",
            "ordId": "*********"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": "0",
                "msg": "",
                "data": [{
                    "clOrdId": "",
                    "ordId": "*********",
                    "sCode": "0",
                    "sMsg": ""
                }]
            }
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/okx/order/cancel",
                headers=headers,
                json=cancel_data
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == "0"

    def test_get_leverage_info_success(self, headers, mock_user_api):
        """测试获取杠杆倍数成功"""
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": "0",
                "msg": "",
                "data": [{
                    "instId": "BTC-USDT-SWAP",
                    "mgnMode": "cross",
                    "lever": "3",
                    "ccy": ""
                }]
            }
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/okx/leverage-info?instId=BTC-USDT-SWAP&mgnMode=cross",
                headers=headers
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == "0"
            assert len(data["data"]) > 0

    def test_set_leverage_success(self, headers, mock_user_api):
        """测试设置杠杆倍数成功"""
        leverage_data = {
            "lever": "5",
            "mgnMode": "cross",
            "instId": "BTC-USDT-SWAP"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": "0",
                "msg": "",
                "data": [{
                    "instId": "BTC-USDT-SWAP",
                    "lever": "5",
                    "mgnMode": "cross",
                    "posSide": ""
                }]
            }
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/okx/set-leverage",
                headers=headers,
                json=leverage_data
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == "0"

    def test_transfer_success(self, headers, mock_user_api):
        """测试资金划转成功"""
        transfer_data = {
            "ccy": "USDT",
            "amt": "100",
            "from": "6",
            "to": "18",
            "type": "0"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": "0",
                "msg": "",
                "data": [{
                    "transId": "*********",
                    "ccy": "USDT",
                    "amt": "100",
                    "from": "6",
                    "to": "18",
                    "type": "0"
                }]
            }
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/okx/transfer",
                headers=headers,
                json=transfer_data
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == "0"
            assert "transId" in data["data"][0]

    def test_get_positions_success(self, headers, mock_user_api):
        """测试获取持仓成功"""
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": "0",
                "msg": "",
                "data": [{
                    "instId": "BTC-USDT-SWAP",
                    "pos": "1",
                    "posSide": "net",
                    "avgPx": "50000",
                    "lever": "3",
                    "upl": "100",
                    "uTime": "1640995200000"
                }]
            }
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/okx/positions?instType=SWAP",
                headers=headers
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == "0"
            assert len(data["data"]) > 0

    def test_network_error_handling(self, headers):
        """测试网络异常处理"""
        with patch('requests.get') as mock_get:
            mock_get.side_effect = requests.exceptions.RequestException(
                "Network error")

            response = requests.get(
                f"{BASE_URL}/api/okx/account/balance", headers=headers)
            # 应该返回错误信息而不是崩溃
            assert response.status_code in [500, 400]

    def test_invalid_json_response(self, headers):
        """测试无效JSON响应处理"""
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.side_effect = json.JSONDecodeError(
                "Invalid JSON", "", 0)
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/okx/account/balance", headers=headers)
            # 应该处理JSON解析错误
            assert response.status_code in [500, 400]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
