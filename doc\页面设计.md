# OKX量化交易系统-页面设计

## 1. 登录/注册页
- 用户名、密码输入，表单校验，登录/注册按钮，错误提示

## 2. 策略管理页
- 策略列表、创建/编辑/删除策略、策略详情弹窗
- 支持策略回测、实盘切换

## 3. 订单管理页
- 订单列表、下单/撤单、订单详情
- 状态筛选、分页

## 4. AI分析页
- AI分析历史列表、生成分析表单、AI建议展示

## 5. 统计看板页
- 账户数、策略数、订单数、AI分析数统计卡片
- ECharts 柱状图展示系统数据分布

## 6. 账户信息页
- 个人信息（头像、用户名、角色、ID）
- 账户余额、持仓列表

## 7. OKX相关页面
### 7.1 OKX持仓页（OKXPositions.vue）
- 实时展示合约持仓，表格字段：产品ID、方向、杠杆、持仓量、开仓均价、未实现盈亏、更新时间
- 支持WebSocket推送和手动刷新，断线重连、异常提示

### 7.2 OKX杠杆设置页（OKXLeverage.vue）
- 查询与设置指定产品/币种的杠杆倍数，表单输入产品ID、保证金模式、币种、杠杆倍数
- 查询结果表格，设置后自动刷新，表单校验、loading、消息提示

### 7.3 OKX资金划转页（OKXTransfer.vue）
- 账户间资金划转，表单输入币种、数量、来源账户、目标账户、划转类型、子账户（可选）
- 划转结果与错误提示，表单校验、loading

### 7.4 OKX历史订单页（OKXOrderHistory.vue）
- 历史订单查询，表单筛选产品ID、订单状态，表格分页展示订单ID、产品、方向、类型、价格、数量、状态、成交量、下单时间
- 支持分页、手动刷新、loading、异常提示

## 8. 全局导航栏
- 动态菜单，显示用户名/头像，退出登录
- 权限控制，未登录自动跳转登录页
- OKX相关页面入口统一集成

## 9. 页面交互与校验
- 表单输入校验、loading状态、消息提示
- WebSocket实时刷新，断线重连、异常友好提示
- 权限不足自动跳转403 