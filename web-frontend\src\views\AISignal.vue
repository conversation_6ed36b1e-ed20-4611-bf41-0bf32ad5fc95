<template>
  <div class="ai-signal">
    <h2>AI交易信号</h2>
    <a-form layout="inline" :model="query" @submit.prevent style="margin-bottom:16px">
      <a-form-item label="产品ID">
        <a-input v-model="query.instId" placeholder="如 BTC-USDT-SWAP" style="width:180px" />
      </a-form-item>
      <a-form-item label="信号类型">
        <a-select v-model="query.type" style="width:120px" allow-clear>
          <a-select-option value="buy">买入信号</a-select-option>
          <a-select-option value="sell">卖出信号</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="fetchSignals" :loading="loading">查询</a-button>
      </a-form-item>
    </a-form>

    <a-table :dataSource="signalList" :columns="columns" :loading="loading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          <a-tag :color="record.type === 'buy' ? 'green' : 'red'">
            {{ record.type === 'buy' ? '买入信号' : '卖出信号' }}
          </a-tag>
        </template>
        <template v-else-if="column.key === 'strength'">
          <a-progress :percent="Number((record.strength * 100).toFixed(2))" :status="getStrengthStatus(record.strength)" />
        </template>
        <template v-else-if="column.key === 'details'">
          <a-button type="link" @click="showSignalDetails(record)">详情</a-button>
        </template>
        <template v-else>
          {{ record[column.dataIndex] }}
        </template>
      </template>
      <template #empty>
        <div style="text-align:center; padding:32px;">暂无信号</div>
      </template>
    </a-table>

    <a-modal
      title="信号详情"
      :open="modalVisible"
      @update:open="modalVisible = $event"
      :footer="null"
      @cancel="modalVisible = false"
    >
      <a-descriptions bordered>
        <a-descriptions-item label="产品ID">{{ currentSignal.instId }}</a-descriptions-item>
        <a-descriptions-item label="信号类型">
          <a-tag :color="currentSignal.type === 'buy' ? 'green' : 'red'">
            {{ currentSignal.type === 'buy' ? '买入信号' : '卖出信号' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="当前价格">{{ currentSignal.currentPrice }}</a-descriptions-item>
        <a-descriptions-item label="建议价格">{{ currentSignal.suggestedPrice }}</a-descriptions-item>
        <a-descriptions-item label="止损价格">{{ currentSignal.stopLoss }}</a-descriptions-item>
        <a-descriptions-item label="目标价格">{{ currentSignal.targetPrice }}</a-descriptions-item>
        <a-descriptions-item label="信号强度">{{ (currentSignal.strength * 100).toFixed(2) }}%</a-descriptions-item>
        <a-descriptions-item label="生成时间">{{ formatTime(currentSignal.timestamp) }}</a-descriptions-item>
        <a-descriptions-item label="分析依据">{{ currentSignal.analysis }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue'
import { ref, reactive } from 'vue'

export default {
  name: 'AISignal',
  setup() {
    const loading = ref(false)
    const modalVisible = ref(false)
    const signalList = ref([])
    const currentSignal = ref({})

    const query = reactive({
      instId: '',
      type: undefined
    })

    const columns = [
      { title: '产品ID', dataIndex: 'instId', key: 'instId' },
      { title: '信号类型', dataIndex: 'type', key: 'type' },
      { title: '当前价格', dataIndex: 'currentPrice', key: 'currentPrice' },
      { title: '建议价格', dataIndex: 'suggestedPrice', key: 'suggestedPrice' },
      { title: '止损价格', dataIndex: 'stopLoss', key: 'stopLoss' },
      { title: '目标价格', dataIndex: 'targetPrice', key: 'targetPrice' },
      { title: '信号强度', dataIndex: 'strength', key: 'strength' },
      { title: '操作', key: 'details' }
    ]

    async function fetchSignals() {
      loading.value = true
      try {
        const params = {}
        if (query.instId) params.instId = query.instId
        if (query.type) params.type = query.type

        const response = await fetch('/api/ai/signals?' + new URLSearchParams(params))
        const data = await response.json()

        if (data.code === 0) {
          signalList.value = data.data
        } else {
          message.error(data.msg || '查询失败')
        }
      } catch (error) {
        console.warn('AI信号API不可用:', error.message)
        message.warning('AI信号服务暂时不可用')
      } finally {
        loading.value = false
      }
    }

    function showSignalDetails(record) {
      currentSignal.value = record
      modalVisible.value = true
    }

    function getStrengthStatus(strength) {
      if (strength >= 0.8) return 'success'
      if (strength >= 0.6) return 'normal'
      return 'exception'
    }

    function formatTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(Number(timestamp))
      return date.toLocaleString()
    }

    return {
      loading,
      query,
      columns,
      signalList,
      modalVisible,
      currentSignal,
      fetchSignals,
      showSignalDetails,
      getStrengthStatus,
      formatTime
    }
  }
}
</script>

<style scoped>
.ai-signal {
  max-width: 900px;
  margin: 0 auto;
  padding: 24px;
}
</style>