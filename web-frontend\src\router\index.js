import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'
import StrategyList from '../views/StrategyList.vue'
import OrderList from '../views/OrderList.vue'
import AIAnalysis from '../views/AIAnalysis.vue'
import Dashboard from '../views/Dashboard.vue'
import SimpleDashboard from '../views/SimpleDashboard.vue'
import Account from '../views/Account.vue'
import Forbidden from '../views/Forbidden.vue'
import OKXMarket from '../views/OKXMarket.vue'
import OKXAccount from '../views/OKXAccount.vue'
import OKXOrder from '../views/OKXOrder.vue'
import OKXPositions from '../views/OKXPositions.vue'
import OKXLeverage from '../views/OKXLeverage.vue'
import OKXTransfer from '../views/OKXTransfer.vue'
import OKXOrderHistory from '../views/OKXOrderHistory.vue'
import AIPredict from '../views/AIPredict.vue'
import AISignal from '../views/AISignal.vue'
import AIRisk from '../views/AIRisk.vue'
import AIRiskAnalysis from '../views/AIRiskAnalysis.vue'
import AIHistory from '../views/AIHistory.vue'
import Test from '../views/Test.vue'
import APISettings from '../views/APISettings.vue'
import RealTrading from '../views/RealTrading.vue'
import AdvancedCharts from '../views/AdvancedCharts.vue'
import RiskManagement from '../views/RiskManagement.vue'
import ChartTest from '../views/ChartTest.vue'
import SimpleCharts from '../views/SimpleCharts.vue'
import PriceAlerts from '../views/PriceAlerts.vue'
import { useUserStore } from '../stores/user'

const routes = [
  { path: '/', component: SimpleDashboard, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/test', component: Test },
  { path: '/login', component: Login },
  { path: '/register', component: Register },
  { path: '/strategies', component: StrategyList, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/orders', component: OrderList, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/ai', component: AIAnalysis, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/ai/predict', component: AIPredict, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/ai/signal', component: AISignal, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/ai/risk', component: AIRisk, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/ai/risk-analysis', component: AIRiskAnalysis, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/ai/history', component: AIHistory, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/dashboard', component: Dashboard, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/account', component: Account, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/settings/api', component: APISettings, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/trading/real', component: RealTrading, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/charts/advanced', component: AdvancedCharts, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/risk/management', component: RiskManagement, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/test/chart', component: ChartTest, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/charts/simple', component: SimpleCharts, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/alerts/price', component: PriceAlerts, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/403', component: Forbidden },
  { path: '/okx/market', component: OKXMarket, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/okx/account', component: OKXAccount, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/okx/order', component: OKXOrder, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/okx/positions', component: OKXPositions, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/okx/leverage', component: OKXLeverage, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/okx/transfer', component: OKXTransfer, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/okx/orders', component: OKXOrderHistory, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/okx/order-history', component: OKXOrderHistory, meta: { requiresAuth: true, roles: ['user', 'admin'] } },
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫：未登录跳转到登录页，权限不足跳转 403
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const userStore = useUserStore()

  // 如果是登录或注册页面，直接通过
  if (to.path === '/login' || to.path === '/register') {
    next()
    return
  }

  // 如果需要认证但没有token，跳转到登录页
  if (to.meta.requiresAuth && !token) {
    next('/login')
    return
  }

  // 检查角色权限
  if (to.meta.roles && !to.meta.roles.includes(userStore.role)) {
    next('/403')
    return
  }

  next()
})

export default router