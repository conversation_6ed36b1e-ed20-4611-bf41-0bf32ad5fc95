from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from webapp.db import get_db
from webapp.api.user_api import get_current_user
from models import AIAnalysis, User
import requests
import json
import time
from typing import Optional, List
from pydantic import BaseModel

router = APIRouter()

# AI分析请求模型


class PredictRequest(BaseModel):
    symbol: str
    timeframe: str = "1h"
    prediction_days: int = 7


class SignalRequest(BaseModel):
    symbol: str
    strategy: str = "trend_following"
    risk_level: str = "medium"


class RiskRequest(BaseModel):
    symbol: str
    position_size: float
    current_price: float

# 新增综合风控分析请求模型


class ComprehensiveRiskRequest(BaseModel):
    include_positions: bool = True
    include_orders: bool = True
    include_account: bool = True
    analysis_type: str = "comprehensive"  # comprehensive, position_only, order_only

# Mock AI服务（可替换为真实AI服务）


class MockAIService:
    @staticmethod
    def predict_price(symbol: str, timeframe: str, days: int) -> dict:
        """模拟价格预测"""
        import random
        base_price = 50000 if "BTC" in symbol else 3000 if "ETH" in symbol else 1
        predictions = []
        current_price = base_price

        for i in range(days):
            # 模拟价格波动
            change = random.uniform(-0.05, 0.05)  # ±5%波动
            current_price *= (1 + change)
            predictions.append({
                "date": f"2024-01-{i+1:02d}",
                "price": round(current_price, 2),
                "confidence": round(random.uniform(0.6, 0.9), 2)
            })

        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "predictions": predictions,
            "trend": "bullish" if predictions[-1]["price"] > base_price else "bearish",
            "confidence": round(sum(p["confidence"] for p in predictions) / len(predictions), 2)
        }

    @staticmethod
    def generate_signal(symbol: str, strategy: str, risk_level: str) -> dict:
        """模拟交易信号生成"""
        import random
        signals = ["buy", "sell", "hold"]
        signal = random.choice(signals)

        return {
            "symbol": symbol,
            "signal": signal,
            "strategy": strategy,
            "risk_level": risk_level,
            "confidence": round(random.uniform(0.7, 0.95), 2),
            "reason": f"基于{strategy}策略的{signal}信号",
            "target_price": round(50000 * random.uniform(0.9, 1.1), 2),
            "stop_loss": round(50000 * random.uniform(0.85, 0.95), 2),
            "take_profit": round(50000 * random.uniform(1.05, 1.15), 2)
        }

    @staticmethod
    def risk_analysis(symbol: str, position_size: float, current_price: float) -> dict:
        """模拟风险分析"""
        import random

        # 计算风险指标
        volatility = random.uniform(0.02, 0.08)  # 2-8%波动率
        var_95 = position_size * current_price * volatility * 1.645  # 95% VaR
        max_loss = position_size * current_price * 0.1  # 最大损失10%

        risk_score = min(
            100, (var_95 / (position_size * current_price)) * 1000)

        return {
            "symbol": symbol,
            "position_size": position_size,
            "current_price": current_price,
            "volatility": round(volatility * 100, 2),
            "var_95": round(var_95, 2),
            "max_loss": round(max_loss, 2),
            "risk_score": round(risk_score, 2),
            "risk_level": "high" if risk_score > 70 else "medium" if risk_score > 30 else "low",
            "recommendation": "建议减仓" if risk_score > 70 else "建议持有" if risk_score > 30 else "建议加仓"
        }

    @staticmethod
    def comprehensive_risk_analysis(user_data: dict) -> dict:
        """综合风控分析"""
        import random

        positions = user_data.get("positions", [])
        account = user_data.get("account", [])
        summary = user_data.get("summary", {})

        # 分析持仓风险
        position_risks = []
        total_risk_score = 0

        for pos in positions:
            pos_size = float(pos.get("pos", 0))
            if pos_size == 0:
                continue

            avg_px = float(pos.get("avgPx", 0))
            upl = float(pos.get("upl", 0))
            margin = float(pos.get("margin", 0))
            lever = float(pos.get("lever", 1))

            # 计算单个持仓风险
            pnl_ratio = abs(upl) / (pos_size *
                                    avg_px) if pos_size * avg_px > 0 else 0
            margin_ratio = margin / \
                (pos_size * avg_px) if pos_size * avg_px > 0 else 0

            risk_score = min(100, (pnl_ratio + margin_ratio) * 100)

            position_risks.append({
                "instId": pos.get("instId"),
                "posSide": pos.get("posSide"),
                "risk_score": round(risk_score, 2),
                "risk_level": "high" if risk_score > 70 else "medium" if risk_score > 30 else "low",
                "pnl_ratio": round(pnl_ratio * 100, 2),
                "margin_ratio": round(margin_ratio * 100, 2),
                "recommendation": "建议减仓" if risk_score > 70 else "建议持有" if risk_score > 30 else "风险可控"
            })

            total_risk_score += risk_score

        # 计算整体风险
        avg_risk_score = total_risk_score / \
            len(position_risks) if position_risks else 0

        # 生成建议
        recommendations = []
        if avg_risk_score > 70:
            recommendations.append("整体风险较高，建议减仓或设置止损")
        elif avg_risk_score > 30:
            recommendations.append("风险中等，建议关注市场变化")
        else:
            recommendations.append("风险可控，可考虑适度加仓")

        if summary.get("total_positions", 0) > 5:
            recommendations.append("持仓数量较多，建议集中持仓降低管理难度")

        if summary.get("pending_orders_count", 0) > 10:
            recommendations.append("挂单数量较多，建议清理不必要的挂单")

        return {
            "overall_risk_score": round(avg_risk_score, 2),
            "overall_risk_level": "high" if avg_risk_score > 70 else "medium" if avg_risk_score > 30 else "low",
            "position_risks": position_risks,
            "recommendations": recommendations,
            "summary": {
                "total_positions": summary.get("total_positions", 0),
                "total_unrealized_pnl": summary.get("total_unrealized_pnl", 0),
                "total_margin": summary.get("total_margin", 0),
                "pending_orders_count": summary.get("pending_orders_count", 0),
                "recommendation": "; ".join(recommendations)
            },
            "analysis_time": int(time.time() * 1000)
        }


# AI服务实例
ai_service = MockAIService()


@router.post("/predict")
async def predict_price(
    request: PredictRequest,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """行情预测API"""
    try:
        # 调用AI服务进行预测
        prediction = ai_service.predict_price(
            request.symbol,
            request.timeframe,
            request.prediction_days
        )

        # 保存预测结果到数据库
        ai_analysis = AIAnalysis(
            user_id=current_user.id,
            type="price_prediction",
            content=json.dumps(prediction),
            suggestion=f"预测趋势: {prediction['trend']}, 置信度: {prediction['confidence']}"
        )
        db.add(ai_analysis)
        db.commit()

        return {
            "code": 0,
            "msg": "预测成功",
            "data": prediction
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"预测失败: {str(e)}",
            "data": None
        }


@router.post("/signal")
async def generate_signal(
    request: SignalRequest,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """交易信号生成API"""
    try:
        # 调用AI服务生成信号
        signal = ai_service.generate_signal(
            request.symbol,
            request.strategy,
            request.risk_level
        )

        # 保存信号到数据库
        ai_analysis = AIAnalysis(
            user_id=current_user.id,
            type="trading_signal",
            content=json.dumps(signal),
            suggestion=f"信号: {signal['signal']}, 置信度: {signal['confidence']}"
        )
        db.add(ai_analysis)
        db.commit()

        return {
            "code": 0,
            "msg": "信号生成成功",
            "data": signal
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"信号生成失败: {str(e)}",
            "data": None
        }


@router.post("/risk")
async def risk_analysis(
    request: RiskRequest,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """智能风控分析API"""
    try:
        # 调用AI服务进行风险分析
        risk = ai_service.risk_analysis(
            request.symbol,
            request.position_size,
            request.current_price
        )

        # 保存风险分析到数据库
        ai_analysis = AIAnalysis(
            user_id=current_user.id,
            type="risk_analysis",
            content=json.dumps(risk),
            suggestion=risk["recommendation"]
        )
        db.add(ai_analysis)
        db.commit()

        return {
            "code": 0,
            "msg": "风险分析成功",
            "data": risk
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"风险分析失败: {str(e)}",
            "data": None
        }


@router.post("/comprehensive-risk")
async def comprehensive_risk_analysis(
    request: ComprehensiveRiskRequest,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """综合风控分析API - 基于用户实际资金、持仓、订单数据"""
    try:
        # 1. 获取用户综合数据
        dashboard_response = await get_user_dashboard_data(current_user, db)
        if dashboard_response["code"] != 0 and dashboard_response["code"] != "0":
            return dashboard_response

        user_data = dashboard_response["data"]

        # 2. 根据请求类型筛选数据
        analysis_data = {
            "user_id": current_user.id,
            "timestamp": int(time.time() * 1000)
        }

        if request.include_account:
            analysis_data["account"] = user_data["account"]
            analysis_data["summary"] = user_data["summary"]

        if request.include_positions:
            analysis_data["positions"] = user_data["positions"]

        if request.include_orders:
            analysis_data["pending_orders"] = user_data["pending_orders"]
            analysis_data["history_orders"] = user_data["history_orders"]

        # 3. 调用AI服务进行综合分析
        ai_result = ai_service.comprehensive_risk_analysis(analysis_data)

        # 4. 保存分析结果到数据库
        ai_analysis = AIAnalysis(
            user_id=current_user.id,
            type="comprehensive_risk_analysis",
            content=json.dumps(ai_result),
            suggestion=ai_result.get("summary", {}).get(
                "recommendation", "无特殊建议")
        )
        db.add(ai_analysis)
        db.commit()

        return {
            "code": 0,
            "msg": "综合风控分析完成",
            "data": ai_result
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"综合风控分析失败: {str(e)}",
            "data": None
        }


@router.get("/history")
async def get_ai_history(
    type: Optional[str] = None,
    limit: int = 20,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取AI分析历史"""
    try:
        query = db.query(AIAnalysis).filter(
            AIAnalysis.user_id == current_user.id
        )

        if type:
            query = query.filter(AIAnalysis.type == type)

        history = query.order_by(
            AIAnalysis.created_at.desc()).limit(limit).all()

        result = []
        for item in history:
            try:
                content = json.loads(item.content) if item.content else {}
            except:
                content = {}

            result.append({
                "id": item.id,
                "type": item.type,
                "content": content,
                "suggestion": item.suggestion,
                "created_at": item.created_at.isoformat()
            })

        return {
            "code": 0,
            "msg": "获取成功",
            "data": result
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"获取失败: {str(e)}",
            "data": []
        }


@router.get("/stats")
async def get_ai_stats(
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取AI分析统计"""
    try:
        total = db.query(AIAnalysis).filter(
            AIAnalysis.user_id == current_user.id
        ).count()

        # 按类型统计
        type_stats = db.query(
            AIAnalysis.type,
            db.func.count(AIAnalysis.id)
        ).filter(
            AIAnalysis.user_id == current_user.id
        ).group_by(AIAnalysis.type).all()

        stats = {
            "total_analyses": total,
            "by_type": {item[0]: item[1] for item in type_stats}
        }

        return {
            "code": 0,
            "msg": "获取成功",
            "data": stats
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"获取失败: {str(e)}",
            "data": {}
        }


# 为前端兼容性添加的API端点

@router.get("/list")
async def get_ai_list(
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取AI分析列表（兼容前端）"""
    return await get_ai_history(current_user=current_user, db=db)


@router.post("/generate")
async def generate_ai_analysis(
    request: dict = Body(...),
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """生成AI分析（兼容前端）"""
    try:
        # 保存分析到数据库
        ai_analysis = AIAnalysis(
            user_id=current_user.id,
            type=request.get("type", "行情"),
            content=request.get("content", ""),
            suggestion=f"基于{request.get('type', '行情')}分析的建议"
        )
        db.add(ai_analysis)
        db.commit()

        return {
            "code": 0,
            "msg": "生成成功",
            "data": {
                "id": ai_analysis.id,
                "type": ai_analysis.type,
                "content": ai_analysis.content,
                "suggestion": ai_analysis.suggestion
            }
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"生成失败: {str(e)}",
            "data": None
        }


@router.get("/predictions")
async def get_predictions(
    instId: Optional[str] = None,
    type: Optional[str] = None,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取预测列表（兼容前端）"""
    try:
        # 返回模拟数据
        predictions = [
            {
                "id": 1,
                "instId": instId or "BTC-USDT-SWAP",
                "type": type or "price",
                "currentPrice": 50000,
                "predictedPrice": 52000,
                "predictedTrend": "bullish",
                "confidence": 0.85,
                "created_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": 2,
                "instId": "ETH-USDT-SWAP",
                "type": "trend",
                "currentPrice": 3000,
                "predictedPrice": 3200,
                "predictedTrend": "bullish",
                "confidence": 0.78,
                "created_at": "2024-01-01T01:00:00Z"
            }
        ]

        return {
            "code": 0,
            "msg": "获取成功",
            "data": predictions
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"获取失败: {str(e)}",
            "data": []
        }


@router.get("/signals")
async def get_signals(
    instId: Optional[str] = None,
    type: Optional[str] = None,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取信号列表（兼容前端）"""
    try:
        # 返回模拟数据
        signals = [
            {
                "id": 1,
                "instId": instId or "BTC-USDT-SWAP",
                "type": type or "buy",
                "currentPrice": 50000,
                "suggestedPrice": 49800,
                "stopLoss": 48000,
                "targetPrice": 52000,
                "strength": 0.85,
                "created_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": 2,
                "instId": "ETH-USDT-SWAP",
                "type": "sell",
                "currentPrice": 3000,
                "suggestedPrice": 3020,
                "stopLoss": 3100,
                "targetPrice": 2800,
                "strength": 0.72,
                "created_at": "2024-01-01T01:00:00Z"
            }
        ]

        return {
            "code": 0,
            "msg": "获取成功",
            "data": signals
        }
    except Exception as e:
        return {
            "code": 1,
            "msg": f"获取失败: {str(e)}",
            "data": []
        }


async def get_user_dashboard_data(current_user, db: Session):
    """获取用户仪表盘数据"""
    try:
        # 这里直接调用OKX API获取数据，避免循环依赖
        api_key, api_secret, passphrase = get_user_api(
            current_user.id, db)

        # 获取账户余额
        balance_data = get_account_balance_internal(
            api_key, api_secret, passphrase)

        # 获取持仓信息
        positions_data = get_positions_internal(
            api_key, api_secret, passphrase)

        # 获取未成交订单
        pending_orders_data = get_pending_orders_internal(
            api_key, api_secret, passphrase)

        # 获取历史订单
        history_orders_data = get_order_history_internal(
            api_key, api_secret, passphrase, limit=50)

        # 计算汇总信息
        summary = calculate_dashboard_summary(
            balance_data, positions_data, pending_orders_data)

        return {
            "code": 0,
            "data": {
                "account": balance_data,
                "positions": positions_data,
                "pending_orders": pending_orders_data,
                "history_orders": history_orders_data,
                "summary": summary
            }
        }
    except Exception as e:
        return {"code": 1, "msg": str(e)}

# 从okx_api.py复制内部方法（避免循环导入）


def get_user_api(user_id: int, db: Session):
    """获取用户API配置"""
    user_api = db.query(User).filter(
        User.id == user_id).first()
    if not user_api:
        raise Exception("用户API配置不存在")
    return user_api.api_key, user_api.api_secret, user_api.passphrase


def okx_sign(api_secret: str, method: str, path: str, body: str):
    """OKX签名"""
    import hmac
    import hashlib
    import base64
    timestamp = str(int(time.time()) + 28800)  # UTC+8
    message = timestamp + method + path + body
    mac = hmac.new(
        bytes(api_secret, encoding='utf8'),
        bytes(message, encoding='utf-8'),
        digestmod='sha256'
    )
    d = mac.digest()
    return timestamp, base64.b64encode(d).decode()


def get_account_balance_internal(api_key, api_secret, passphrase):
    """内部方法：获取账户余额"""
    path = "/api/v5/account/balance"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if data.get("code") == "0":
            return data.get("data", [])
    return []


def get_positions_internal(api_key, api_secret, passphrase):
    """内部方法：获取持仓信息"""
    path = "/api/v5/account/positions"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if data.get("code") == "0":
            return data.get("data", [])
    return []


def get_pending_orders_internal(api_key, api_secret, passphrase):
    """内部方法：获取未成交订单"""
    path = "/api/v5/trade/orders-pending"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if data.get("code") == "0":
            return data.get("data", [])
    return []


def get_order_history_internal(api_key, api_secret, passphrase, limit=50):
    """内部方法：获取历史订单"""
    path = f"/api/v5/trade/orders-history?limit={limit}"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if data.get("code") == "0":
            return data.get("data", [])
    return []


def calculate_dashboard_summary(balance_data, positions_data, pending_orders_data):
    """计算汇总信息"""
    summary = {
        "total_equity": 0,
        "available_balance": 0,
        "total_positions": 0,
        "total_unrealized_pnl": 0,
        "total_realized_pnl": 0,
        "total_margin": 0,
        "pending_orders_count": len(pending_orders_data),
        "risk_score": 0,
        "position_breakdown": {}
    }

    # 计算账户资金
    for account in balance_data:
        for detail in account.get("details", []):
            if detail.get("ccy") == "USDT":
                summary["total_equity"] = float(detail.get("eq", 0))
                summary["available_balance"] = float(detail.get("availEq", 0))
                break

    # 计算持仓汇总
    for position in positions_data:
        pos = float(position.get("pos", 0))
        if pos != 0:  # 只计算有持仓的
            summary["total_positions"] += 1
            summary["total_unrealized_pnl"] += float(position.get("upl", 0))
            summary["total_realized_pnl"] += float(
                position.get("realizedPnl", 0))
            summary["total_margin"] += float(position.get("margin", 0))

            # 按产品类型统计
            inst_type = position.get("instType", "UNKNOWN")
            if inst_type not in summary["position_breakdown"]:
                summary["position_breakdown"][inst_type] = {
                    "count": 0,
                    "total_pnl": 0,
                    "total_margin": 0
                }
            summary["position_breakdown"][inst_type]["count"] += 1
            summary["position_breakdown"][inst_type]["total_pnl"] += float(
                position.get("upl", 0))
            summary["position_breakdown"][inst_type]["total_margin"] += float(
                position.get("margin", 0))

    # 计算风险评分（简单算法）
    if summary["total_equity"] > 0:
        margin_ratio = summary["total_margin"] / summary["total_equity"]
        pnl_ratio = abs(summary["total_unrealized_pnl"]
                        ) / summary["total_equity"]
        summary["risk_score"] = min(100, (margin_ratio + pnl_ratio) * 50)

    return summary
