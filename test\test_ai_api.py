import pytest
from fastapi.testclient import TestClient
from webapp.main import app
import json

client = TestClient(app)


@pytest.fixture
def auth_headers():
    """获取认证头"""
    # 先登录获取token
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    response = client.post("/api/user/login", json=login_data)
    if response.status_code == 200:
        token = response.json().get("access_token")
        return {"Authorization": f"Bearer {token}"}
    return {}


class TestAIPredict:
    """测试AI行情预测功能"""

    def test_predict_price_success(self, auth_headers):
        """测试价格预测成功"""
        data = {
            "symbol": "BTC-USDT",
            "timeframe": "1h",
            "prediction_days": 7
        }
        response = client.post(
            "/api/ai/predict", json=data, headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "0"
        assert "data" in result
        assert result["data"]["symbol"] == "BTC-USDT"
        assert result["data"]["timeframe"] == "1h"
        assert len(result["data"]["predictions"]) == 7

    def test_predict_price_invalid_symbol(self, auth_headers):
        """测试无效交易对"""
        data = {
            "symbol": "",
            "timeframe": "1h",
            "prediction_days": 7
        }
        response = client.post(
            "/api/ai/predict", json=data, headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "1"

    def test_predict_price_unauthorized(self):
        """测试未授权访问"""
        data = {
            "symbol": "BTC-USDT",
            "timeframe": "1h",
            "prediction_days": 7
        }
        response = client.post("/api/ai/predict", json=data)
        assert response.status_code == 401


class TestAISignal:
    """测试AI交易信号功能"""

    def test_generate_signal_success(self, auth_headers):
        """测试信号生成成功"""
        data = {
            "symbol": "BTC-USDT",
            "strategy": "trend_following",
            "risk_level": "medium"
        }
        response = client.post(
            "/api/ai/signal", json=data, headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "0"
        assert "data" in result
        assert result["data"]["symbol"] == "BTC-USDT"
        assert result["data"]["strategy"] == "trend_following"
        assert result["data"]["risk_level"] == "medium"
        assert "signal" in result["data"]
        assert "confidence" in result["data"]

    def test_generate_signal_different_strategies(self, auth_headers):
        """测试不同策略的信号生成"""
        strategies = ["trend_following",
                      "mean_reversion", "breakout", "momentum"]
        for strategy in strategies:
            data = {
                "symbol": "ETH-USDT",
                "strategy": strategy,
                "risk_level": "low"
            }
            response = client.post(
                "/api/ai/signal", json=data, headers=auth_headers)
            assert response.status_code == 200
            result = response.json()
            assert result["code"] == "0"
            assert result["data"]["strategy"] == strategy


class TestAIRisk:
    """测试AI风险分析功能"""

    def test_risk_analysis_success(self, auth_headers):
        """测试风险分析成功"""
        data = {
            "symbol": "BTC-USDT",
            "position_size": 1.0,
            "current_price": 50000.0
        }
        response = client.post("/api/ai/risk", json=data, headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "0"
        assert "data" in result
        assert result["data"]["symbol"] == "BTC-USDT"
        assert result["data"]["position_size"] == 1.0
        assert result["data"]["current_price"] == 50000.0
        assert "risk_score" in result["data"]
        assert "risk_level" in result["data"]
        assert "volatility" in result["data"]
        assert "var_95" in result["data"]
        assert "max_loss" in result["data"]

    def test_risk_analysis_invalid_position(self, auth_headers):
        """测试无效持仓大小"""
        data = {
            "symbol": "BTC-USDT",
            "position_size": -1.0,
            "current_price": 50000.0
        }
        response = client.post("/api/ai/risk", json=data, headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "1"

    def test_risk_analysis_different_risk_levels(self, auth_headers):
        """测试不同风险等级的分析"""
        test_cases = [
            {"position_size": 0.1, "current_price": 50000.0},  # 低风险
            {"position_size": 1.0, "current_price": 50000.0},  # 中风险
            {"position_size": 5.0, "current_price": 50000.0},  # 高风险
        ]

        for case in test_cases:
            data = {
                "symbol": "BTC-USDT",
                **case
            }
            response = client.post(
                "/api/ai/risk", json=data, headers=auth_headers)
            assert response.status_code == 200
            result = response.json()
            assert result["code"] == "0"
            assert "risk_level" in result["data"]


class TestAIHistory:
    """测试AI分析历史功能"""

    def test_get_ai_history_success(self, auth_headers):
        """测试获取AI分析历史成功"""
        response = client.get("/api/ai/history", headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "0"
        assert "data" in result
        assert isinstance(result["data"], list)

    def test_get_ai_history_with_type_filter(self, auth_headers):
        """测试按类型筛选历史记录"""
        response = client.get(
            "/api/ai/history?type=price_prediction", headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "0"
        assert "data" in result

    def test_get_ai_history_with_limit(self, auth_headers):
        """测试限制返回数量"""
        response = client.get("/api/ai/history?limit=5", headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "0"
        assert "data" in result
        assert len(result["data"]) <= 5

    def test_get_ai_history_unauthorized(self):
        """测试未授权访问历史记录"""
        response = client.get("/api/ai/history")
        assert response.status_code == 401


class TestAIStats:
    """测试AI分析统计功能"""

    def test_get_ai_stats_success(self, auth_headers):
        """测试获取AI分析统计成功"""
        response = client.get("/api/ai/stats", headers=auth_headers)
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == "0"
        assert "data" in result
        assert "total_analyses" in result["data"]
        assert "by_type" in result["data"]

    def test_get_ai_stats_unauthorized(self):
        """测试未授权访问统计"""
        response = client.get("/api/ai/stats")
        assert response.status_code == 401


class TestAIEndToEnd:
    """端到端测试AI分析流程"""

    def test_ai_analysis_workflow(self, auth_headers):
        """测试完整的AI分析流程"""
        # 1. 进行价格预测
        predict_data = {
            "symbol": "BTC-USDT",
            "timeframe": "1h",
            "prediction_days": 3
        }
        predict_response = client.post(
            "/api/ai/predict", json=predict_data, headers=auth_headers)
        assert predict_response.status_code == 200
        predict_result = predict_response.json()
        assert predict_result["code"] == "0"

        # 2. 生成交易信号
        signal_data = {
            "symbol": "BTC-USDT",
            "strategy": "trend_following",
            "risk_level": "medium"
        }
        signal_response = client.post(
            "/api/ai/signal", json=signal_data, headers=auth_headers)
        assert signal_response.status_code == 200
        signal_result = signal_response.json()
        assert signal_result["code"] == "0"

        # 3. 进行风险分析
        risk_data = {
            "symbol": "BTC-USDT",
            "position_size": 1.0,
            "current_price": 50000.0
        }
        risk_response = client.post(
            "/api/ai/risk", json=risk_data, headers=auth_headers)
        assert risk_response.status_code == 200
        risk_result = risk_response.json()
        assert risk_result["code"] == "0"

        # 4. 检查历史记录
        history_response = client.get("/api/ai/history", headers=auth_headers)
        assert history_response.status_code == 200
        history_result = history_response.json()
        assert history_result["code"] == "0"
        assert len(history_result["data"]) >= 3  # 至少应该有3条记录

        # 5. 检查统计信息
        stats_response = client.get("/api/ai/stats", headers=auth_headers)
        assert stats_response.status_code == 200
        stats_result = stats_response.json()
        assert stats_result["code"] == "0"
        assert stats_result["data"]["total_analyses"] >= 3
