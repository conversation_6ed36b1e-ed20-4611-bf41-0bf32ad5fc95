#!/usr/bin/env python3
"""
测试注册和登录流程的脚本
"""

import requests
import json
import time

def test_register_and_login():
    base_url = "http://localhost:8000/api/user"
    
    # 测试数据
    test_user = {
        "username": "testuser123",
        "password": "testpass123"
    }
    
    print("=== 测试注册功能 ===")
    try:
        register_url = f"{base_url}/register"
        print(f"注册URL: {register_url}")
        print(f"注册数据: {test_user}")
        
        response = requests.post(register_url, json=test_user)
        print(f"注册响应状态码: {response.status_code}")
        print(f"注册响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                print("✅ 注册成功！")
            else:
                print(f"❌ 注册失败: {data.get('msg', 'Unknown error')}")
                return
        else:
            print(f"❌ 注册HTTP错误: {response.status_code}")
            return
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器")
        return
    except Exception as e:
        print(f"❌ 注册测试失败: {e}")
        return
    
    # 等待一秒
    time.sleep(1)
    
    print("\n=== 测试登录功能 ===")
    try:
        login_url = f"{base_url}/login"
        print(f"登录URL: {login_url}")
        print(f"登录数据: {test_user}")
        
        response = requests.post(login_url, json=test_user)
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                print("✅ 登录成功！")
                print(f"Token: {data.get('token', 'N/A')[:50]}...")
                print(f"过期时间: {data.get('exp', 'N/A')}")
            else:
                print(f"❌ 登录失败: {data.get('msg', 'Unknown error')}")
        else:
            print(f"❌ 登录HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 登录测试失败: {e}")

if __name__ == "__main__":
    test_register_and_login()
