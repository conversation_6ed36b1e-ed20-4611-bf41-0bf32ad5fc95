from fastapi import APIRouter, Query, Request, Depends, HTTPException, Body
from okx import Account, Trade, Status, PublicData
from okx.MarketData import MarketAPI
from ..okx_config import get_okx_config
from ..models import User
from ..db import get_db
from .user_api import get_current_user, decrypt_api_secret, okx_sign
import requests
import time
import base64
import hmac
import hashlib
import json
from datetime import datetime
from sqlalchemy.orm import Session

# 默认配置（作为后备）
try:
    cfg = get_okx_config()
    default_api_key = cfg['api_key']
    default_secret_key = cfg['secret_key']
    default_passphrase = cfg['passphrase']
    default_flag = cfg['flag']
except:
    default_api_key = ""
    default_secret_key = ""
    default_passphrase = ""
    default_flag = "0"  # 0: 正式环境, 1: 模拟环境

router = APIRouter()


def get_user_okx_config(current_user: dict, db: Session):
    """获取用户的OKX API配置"""
    try:
        db_user = db.query(User).filter(User.id == current_user["user_id"]).first()
        if db_user and db_user.api_key:
            return {
                "api_key": db_user.api_key,
                "secret_key": decrypt_api_secret(db_user.api_secret or ""),
                "passphrase": decrypt_api_secret(db_user.passphrase or ""),
                "flag": "0"  # 暂时使用正式环境
            }
        else:
            # 使用默认配置
            return {
                "api_key": default_api_key,
                "secret_key": default_secret_key,
                "passphrase": default_passphrase,
                "flag": default_flag
            }
    except Exception as e:
        print(f"Error getting user OKX config: {e}")
        return {
            "api_key": default_api_key,
            "secret_key": default_secret_key,
            "passphrase": default_passphrase,
            "flag": default_flag
        }


def make_okx_request(method: str, path: str, body: str = "", config: dict = None):
    """发送OKX API请求"""
    if not config or not config.get("api_key"):
        raise Exception("未配置API密钥")

    base_url = "https://www.okx.com"
    url = f"{base_url}{path}"

    timestamp, signature = okx_sign(
        config["secret_key"],
        method,
        path,
        body
    )

    headers = {
        "OK-ACCESS-KEY": config["api_key"],
        "OK-ACCESS-SIGN": signature,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": config["passphrase"],
        "Content-Type": "application/json"
    }

    if method.upper() == "GET":
        response = requests.get(url, headers=headers, timeout=10)
    elif method.upper() == "POST":
        response = requests.post(url, headers=headers, data=body, timeout=10)
    else:
        raise Exception(f"不支持的HTTP方法: {method}")

    return response


def format_response(result):
    if isinstance(result, dict) and "code" in result:
        return result
    return {"code": "0", "msg": "", "data": result}


def format_error(msg, code="1", data=None):
    return {"code": code, "msg": msg, "data": data or []}


@router.get("/status")
async def get_status():
    try:
        status_api = Status.StatusAPI(flag=flag)
        result = status_api.status()
        return format_response(result)
    except Exception as e:
        return format_error(str(e))


@router.get("/products")
async def get_products(instType: str):
    try:
        public_api = PublicData.PublicAPI(flag=flag)
        result = public_api.get_instruments(instType=instType.upper())
        return format_response(result)
    except Exception as e:
        return format_error(str(e))

# 删除旧的okx_sign函数，使用user_api.py中的新版本

# 删除旧的get_user_api函数，使用新的get_user_okx_config

# 获取OKX行情（无需签名）


@router.get("/market/ticker")
def get_ticker(instId: str):
    url = f"https://www.okx.com/api/v5/market/ticker?instId={instId}"
    resp = requests.get(url)
    if resp.status_code != 200:
        raise HTTPException(status_code=500, detail="OKX接口异常")
    return resp.json()

# 获取OKX账户余额（需签名）


@router.get("/account/balance")
def get_balance(current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    try:
        config = get_user_okx_config(current_user, db)
        response = make_okx_request("GET", "/api/v5/account/balance", "", config)

        if response.status_code == 200:
            return format_response(response.json())
        else:
            return format_error(f"OKX账户余额查询失败: {response.text}", str(response.status_code))
    except Exception as e:
        return format_error(str(e))

# 下单（真实签名转发）


@router.post("/order/create")
def create_order(
    order: dict = Body(...),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    api_key, api_secret, passphrase = get_user_api(current_user["user_id"], db)
    path = "/api/v5/trade/order"
    url = f"https://www.okx.com{path}"
    method = "POST"
    body = json.dumps(order)
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.post(url, headers=headers, data=body)
    if resp.status_code != 200:
        raise HTTPException(status_code=500, detail=f"OKX下单失败: {resp.text}")
    return resp.json()

# 撤单（真实签名转发）


@router.post("/order/cancel")
def cancel_order(
    order: dict = Body(...),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    api_key, api_secret, passphrase = get_user_api(current_user["user_id"], db)
    path = "/api/v5/trade/cancel-order"
    url = f"https://www.okx.com{path}"
    method = "POST"
    body = json.dumps(order)
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.post(url, headers=headers, data=body)
    if resp.status_code != 200:
        raise HTTPException(status_code=500, detail=f"OKX撤单失败: {resp.text}")
    return resp.json()

# 查询订单（真实签名转发）


@router.get("/order/list")
def list_orders(current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    api_key, api_secret, passphrase = get_user_api(current_user["user_id"], db)
    path = "/api/v5/trade/orders-pending"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code != 200:
        raise HTTPException(status_code=500, detail=f"OKX订单查询失败: {resp.text}")
    return resp.json()

# 获取杠杆倍数（需签名）


@router.get("/leverage-info")
def get_leverage_info(
    instId: str = Query(None),
    mgnMode: str = Query(None),
    ccy: str = Query(None),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        api_key, api_secret, passphrase = get_user_api(
            current_user["user_id"], db)
        path = "/api/v5/account/leverage-info"
        url = f"https://www.okx.com{path}"
        method = "GET"
        params = []
        if instId:
            params.append(f"instId={instId}")
        if mgnMode:
            params.append(f"mgnMode={mgnMode}")
        if ccy:
            params.append(f"ccy={ccy}")
        if params:
            url += "?" + "&".join(params)
        body = ''
        timestamp, sign = okx_sign(api_secret, method, path, body)
        headers = {
            "OK-ACCESS-KEY": api_key,
            "OK-ACCESS-SIGN": sign,
            "OK-ACCESS-TIMESTAMP": timestamp,
            "OK-ACCESS-PASSPHRASE": passphrase,
            "Content-Type": "application/json"
        }
        resp = requests.get(url, headers=headers)
        if resp.status_code != 200:
            return format_error(f"OKX杠杆查询失败: {resp.text}")
        return format_response(resp.json())
    except Exception as e:
        return format_error(str(e))

# 设置杠杆倍数（需签名）


@router.post("/set-leverage")
def set_leverage(
    leverage_body: dict = Body(...),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        api_key, api_secret, passphrase = get_user_api(
            current_user["user_id"], db)
        path = "/api/v5/account/set-leverage"
        url = f"https://www.okx.com{path}"
        method = "POST"
        body = json.dumps(leverage_body)
        timestamp, sign = okx_sign(api_secret, method, path, body)
        headers = {
            "OK-ACCESS-KEY": api_key,
            "OK-ACCESS-SIGN": sign,
            "OK-ACCESS-TIMESTAMP": timestamp,
            "OK-ACCESS-PASSPHRASE": passphrase,
            "Content-Type": "application/json"
        }
        resp = requests.post(url, headers=headers, data=body)
        if resp.status_code != 200:
            return format_error(f"OKX设置杠杆失败: {resp.text}")
        return format_response(resp.json())
    except Exception as e:
        return format_error(str(e))

# 资金划转（需签名）


@router.post("/transfer")
def transfer(
    transfer_body: dict = Body(...),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        api_key, api_secret, passphrase = get_user_api(
            current_user["user_id"], db)
        path = "/api/v5/asset/transfer"
        url = f"https://www.okx.com{path}"
        method = "POST"
        body = json.dumps(transfer_body)
        timestamp, sign = okx_sign(api_secret, method, path, body)
        headers = {
            "OK-ACCESS-KEY": api_key,
            "OK-ACCESS-SIGN": sign,
            "OK-ACCESS-TIMESTAMP": timestamp,
            "OK-ACCESS-PASSPHRASE": passphrase,
            "Content-Type": "application/json"
        }
        resp = requests.post(url, headers=headers, data=body)
        if resp.status_code != 200:
            return format_error(f"OKX资金划转失败: {resp.text}")
        return format_response(resp.json())
    except Exception as e:
        return format_error(str(e))

# 获取合约持仓（需签名）


@router.get("/account/positions")
def get_positions(
    instType: str = Query(None),
    instId: str = Query(None),
    posId: str = Query(None),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        config = get_user_okx_config(current_user, db)

        # 构建查询参数
        path = "/api/v5/account/positions"
        params = []
        if instType:
            params.append(f"instType={instType}")
        if instId:
            params.append(f"instId={instId}")
        if posId:
            params.append(f"posId={posId}")

        if params:
            path += "?" + "&".join(params)

        response = make_okx_request("GET", path, "", config)

        if response.status_code == 200:
            return format_response(response.json())
        else:
            return format_error(f"OKX持仓查询失败: {response.text}", str(response.status_code))
    except Exception as e:
        return format_error(str(e))

# 获取用户综合信息（资金+持仓+订单）- 供AI分析使用


@router.get("/dashboard")
def get_user_dashboard(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户综合信息，包含资金、持仓、订单等，供AI分析使用"""
    try:
        api_key, api_secret, passphrase = get_user_api(
            current_user["user_id"], db)

        # 1. 获取账户余额
        balance_data = get_account_balance_internal(
            api_key, api_secret, passphrase)

        # 2. 获取持仓信息
        positions_data = get_positions_internal(
            api_key, api_secret, passphrase)

        # 3. 获取未成交订单
        pending_orders_data = get_pending_orders_internal(
            api_key, api_secret, passphrase)

        # 4. 获取历史订单（最近100条）
        history_orders_data = get_order_history_internal(
            api_key, api_secret, passphrase, limit=100)

        # 5. 计算汇总信息
        summary = calculate_dashboard_summary(
            balance_data, positions_data, pending_orders_data)

        return {
            "code": "0",
            "msg": "获取成功",
            "data": {
                "account": balance_data,
                "positions": positions_data,
                "pending_orders": pending_orders_data,
                "history_orders": history_orders_data,
                "summary": summary,
                "timestamp": int(time.time() * 1000)
            }
        }
    except Exception as e:
        return format_error(f"获取综合信息失败: {str(e)}")


def get_account_balance_internal(api_key, api_secret, passphrase):
    """内部方法：获取账户余额"""
    path = "/api/v5/account/balance"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if data.get("code") == "0":
            return data.get("data", [])
    return []


def get_positions_internal(api_key, api_secret, passphrase):
    """内部方法：获取持仓信息"""
    path = "/api/v5/account/positions"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if data.get("code") == "0":
            return data.get("data", [])
    return []


def get_pending_orders_internal(api_key, api_secret, passphrase):
    """内部方法：获取未成交订单"""
    path = "/api/v5/trade/orders-pending"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if data.get("code") == "0":
            return data.get("data", [])
    return []


def get_order_history_internal(api_key, api_secret, passphrase, limit=100):
    """内部方法：获取历史订单"""
    path = f"/api/v5/trade/orders-history?limit={limit}"
    url = f"https://www.okx.com{path}"
    method = "GET"
    body = ''
    timestamp, sign = okx_sign(api_secret, method, path, body)
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if data.get("code") == "0":
            return data.get("data", [])
    return []


def calculate_dashboard_summary(balance_data, positions_data, pending_orders_data):
    """计算汇总信息"""
    summary = {
        "total_equity": 0,
        "available_balance": 0,
        "total_positions": 0,
        "total_unrealized_pnl": 0,
        "total_realized_pnl": 0,
        "total_margin": 0,
        "pending_orders_count": len(pending_orders_data),
        "risk_score": 0,
        "position_breakdown": {}
    }

    # 计算账户资金
    for account in balance_data:
        for detail in account.get("details", []):
            if detail.get("ccy") == "USDT":
                summary["total_equity"] = float(detail.get("eq", 0))
                summary["available_balance"] = float(detail.get("availEq", 0))
                break

    # 计算持仓汇总
    for position in positions_data:
        pos = float(position.get("pos", 0))
        if pos != 0:  # 只计算有持仓的
            summary["total_positions"] += 1
            summary["total_unrealized_pnl"] += float(position.get("upl", 0))
            summary["total_realized_pnl"] += float(
                position.get("realizedPnl", 0))
            summary["total_margin"] += float(position.get("margin", 0))

            # 按产品类型统计
            inst_type = position.get("instType", "UNKNOWN")
            if inst_type not in summary["position_breakdown"]:
                summary["position_breakdown"][inst_type] = {
                    "count": 0,
                    "total_pnl": 0,
                    "total_margin": 0
                }
            summary["position_breakdown"][inst_type]["count"] += 1
            summary["position_breakdown"][inst_type]["total_pnl"] += float(
                position.get("upl", 0))
            summary["position_breakdown"][inst_type]["total_margin"] += float(
                position.get("margin", 0))

    # 计算风险评分（简单算法）
    if summary["total_equity"] > 0:
        margin_ratio = summary["total_margin"] / summary["total_equity"]
        pnl_ratio = abs(summary["total_unrealized_pnl"]
                        ) / summary["total_equity"]
        summary["risk_score"] = min(100, (margin_ratio + pnl_ratio) * 50)

    return summary

# 你可以继续迁移其他OKX相关接口到此文件
