import pytest
import requests
import json
from unittest.mock import patch, MagicMock

# 测试基础配置
BASE_URL = "http://localhost:8000"


class TestUserAPI:
    """用户API自动化测试用例"""

    def test_user_register_success(self):
        """测试用户注册成功"""
        user_data = {
            "username": "testuser",
            "password": "testpass123"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": 0,
                "msg": "注册成功"
            }
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/user/register",
                json=user_data
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == 0
            assert "注册成功" in data["msg"]

    def test_user_register_duplicate_username(self):
        """测试用户注册-用户名已存在"""
        user_data = {
            "username": "existinguser",
            "password": "testpass123"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 400
            mock_response.json.return_value = {
                "detail": "用户名已存在"
            }
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/user/register",
                json=user_data
            )
            assert response.status_code == 400

    def test_user_login_success(self):
        """测试用户登录成功"""
        login_data = {
            "username": "testuser",
            "password": "testpass123"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": 0,
                "msg": "登录成功",
                "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/user/login",
                json=login_data
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == 0
            assert "token" in data
            assert len(data["token"]) > 0

    def test_user_login_invalid_credentials(self):
        """测试用户登录-无效凭据"""
        login_data = {
            "username": "testuser",
            "password": "wrongpass"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 400
            mock_response.json.return_value = {
                "detail": "用户名或密码错误"
            }
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/user/login",
                json=login_data
            )
            assert response.status_code == 400

    def test_get_user_me_success(self):
        """测试获取当前用户信息成功"""
        headers = {
            "Authorization": "Bearer valid_token_123"
        }

        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": 0,
                "msg": "success",
                "data": {
                    "id": 1,
                    "username": "testuser",
                    "role": "user",
                    "avatar": ""
                }
            }
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/user/me",
                headers=headers
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == 0
            assert data["data"]["username"] == "testuser"

    def test_get_user_me_invalid_token(self):
        """测试获取当前用户信息-无效token"""
        headers = {
            "Authorization": "Bearer invalid_token"
        }

        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 401
            mock_response.json.return_value = {
                "detail": "无效token"
            }
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/user/me",
                headers=headers
            )
            assert response.status_code == 401

    def test_get_user_me_no_token(self):
        """测试获取当前用户信息-无token"""
        response = requests.get(f"{BASE_URL}/api/user/me")
        assert response.status_code == 401

    def test_user_stats_success(self):
        """测试用户统计成功"""
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": 0,
                "msg": "success",
                "data": {
                    "user_count": 10,
                    "strategy_count": 5,
                    "order_count": 20
                }
            }
            mock_get.return_value = mock_response

            response = requests.get(f"{BASE_URL}/api/user/stats")
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == 0
            assert "user_count" in data["data"]
            assert "strategy_count" in data["data"]
            assert "order_count" in data["data"]

    def test_password_validation(self):
        """测试密码验证"""
        # 测试密码长度
        short_password = {
            "username": "testuser",
            "password": "123"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 422  # Validation error
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/user/register",
                json=short_password
            )
            assert response.status_code == 422

    def test_username_validation(self):
        """测试用户名验证"""
        # 测试特殊字符用户名
        invalid_username = {
            "username": "test@user",
            "password": "testpass123"
        }

        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 422  # Validation error
            mock_post.return_value = mock_response

            response = requests.post(
                f"{BASE_URL}/api/user/register",
                json=invalid_username
            )
            assert response.status_code == 422

    def test_token_expiration(self):
        """测试token过期处理"""
        headers = {
            "Authorization": "Bearer expired_token_123"
        }

        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 401
            mock_response.json.return_value = {
                "detail": "token已过期"
            }
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/user/me",
                headers=headers
            )
            assert response.status_code == 401

    def test_user_list_success(self):
        """测试用户列表成功"""
        headers = {
            "Authorization": "Bearer admin_token_123"
        }

        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": 0,
                "msg": "success",
                "data": [
                    {"id": 1, "username": "user1"},
                    {"id": 2, "username": "user2"}
                ]
            }
            mock_get.return_value = mock_response

            response = requests.get(
                f"{BASE_URL}/api/user/list",
                headers=headers
            )
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == 0
            assert len(data["data"]) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
