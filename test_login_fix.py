#!/usr/bin/env python3
"""
测试和修复登录问题
"""

import requests
import json
import sqlite3

def check_database():
    """检查数据库中的用户"""
    try:
        conn = sqlite3.connect('db.sqlite3')
        cursor = conn.cursor()
        
        # 检查用户表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users';")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ 用户表不存在")
            return False
            
        # 查看所有用户
        cursor.execute("SELECT id, username FROM users;")
        users = cursor.fetchall()
        
        print(f"✅ 数据库中的用户: {users}")
        
        conn.close()
        return len(users) > 0
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def create_test_user():
    """创建测试用户"""
    try:
        url = "http://localhost:8002/api/user/register"
        user_data = {
            "username": "admin",
            "password": "admin"
        }
        
        print(f"正在创建测试用户: {user_data['username']}")
        response = requests.post(url, json=user_data)
        
        print(f"注册响应状态码: {response.status_code}")
        print(f"注册响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                print("✅ 测试用户创建成功")
                return True
            else:
                print(f"❌ 创建用户失败: {data.get('msg', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
    
    return False

def test_login():
    """测试登录"""
    try:
        url = "http://localhost:8002/api/user/login"
        login_data = {
            "username": "admin",
            "password": "admin"
        }
        
        print(f"正在测试登录: {login_data['username']}")
        response = requests.post(url, json=login_data)
        
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                print("✅ 登录成功！")
                print(f"Token: {data.get('token', 'N/A')[:50]}...")
                return True
            else:
                print(f"❌ 登录失败: {data.get('msg', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 登录测试失败: {e}")
    
    return False

def test_backend_health():
    """测试后端健康状态"""
    try:
        url = "http://localhost:8002/health"
        response = requests.get(url)
        
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 无法连接到后端服务: {e}")
    
    return False

def main():
    print("=== 登录问题诊断和修复 ===\n")
    
    # 1. 检查后端服务
    print("1. 检查后端服务...")
    if not test_backend_health():
        print("请先启动后端服务: python -m uvicorn webapp.main:app --host 0.0.0.0 --port 8002 --reload")
        return
    
    # 2. 检查数据库
    print("\n2. 检查数据库...")
    has_users = check_database()
    
    # 3. 创建测试用户（如果需要）
    if not has_users:
        print("\n3. 创建测试用户...")
        if not create_test_user():
            print("无法创建测试用户，请检查后端日志")
            return
    
    # 4. 测试登录
    print("\n4. 测试登录...")
    if test_login():
        print("\n✅ 登录功能正常！")
        print("现在可以在前端使用 admin/admin 登录")
    else:
        print("\n❌ 登录功能仍有问题，请检查后端日志")

if __name__ == "__main__":
    main()
