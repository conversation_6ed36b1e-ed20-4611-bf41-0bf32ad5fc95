import pytest
import json
from unittest.mock import patch, MagicMock
from webapp.api.okx_api import get_user_dashboard, calculate_dashboard_summary


class TestOKXDashboard:
    """OKX聚合接口测试"""

    @pytest.fixture
    def mock_user(self):
        """模拟用户数据"""
        return {
            "user_id": 1,
            "username": "testuser",
            "role": "user"
        }

    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return MagicMock()

    @pytest.fixture
    def mock_user_api(self):
        """模拟用户API配置"""
        return {
            "api_key": "test_api_key",
            "api_secret": "test_api_secret",
            "passphrase": "test_passphrase"
        }

    @patch('webapp.api.okx_api.get_user_api')
    @patch('webapp.api.okx_api.get_account_balance_internal')
    @patch('webapp.api.okx_api.get_positions_internal')
    @patch('webapp.api.okx_api.get_pending_orders_internal')
    @patch('webapp.api.okx_api.get_order_history_internal')
    def test_get_user_dashboard_success(
        self,
        mock_get_history,
        mock_get_pending,
        mock_get_positions,
        mock_get_balance,
        mock_get_user_api,
        mock_user,
        mock_db
    ):
        """测试成功获取用户仪表盘数据"""
        # 模拟API配置
        mock_get_user_api.return_value = (
            "test_api_key", "test_api_secret", "test_passphrase"
        )

        # 模拟账户余额数据
        mock_get_balance.return_value = [
            {
                "details": [
                    {
                        "ccy": "USDT",
                        "eq": "10000.0",
                        "availEq": "8000.0",
                        "frozenBal": "0"
                    }
                ]
            }
        ]

        # 模拟持仓数据
        mock_get_positions.return_value = [
            {
                "instId": "BTC-USDT-SWAP",
                "posSide": "long",
                "pos": "1.0",
                "avgPx": "50000.0",
                "upl": "1000.0",
                "margin": "500.0",
                "lever": "10",
                "instType": "SWAP"
            }
        ]

        # 模拟未成交订单
        mock_get_pending.return_value = [
            {
                "instId": "ETH-USDT-SWAP",
                "side": "buy",
                "px": "3000.0",
                "sz": "0.1",
                "state": "live"
            }
        ]

        # 模拟历史订单
        mock_get_history.return_value = [
            {
                "instId": "BTC-USDT-SWAP",
                "side": "buy",
                "px": "50000.0",
                "sz": "1.0",
                "state": "filled"
            }
        ]

        # 调用接口
        result = get_user_dashboard(mock_user, mock_db)

        # 验证结果
        assert result["code"] == "0"
        assert result["msg"] == "获取成功"
        assert "data" in result

        data = result["data"]
        assert "account" in data
        assert "positions" in data
        assert "pending_orders" in data
        assert "history_orders" in data
        assert "summary" in data
        assert "timestamp" in data

        # 验证汇总数据
        summary = data["summary"]
        assert summary["total_equity"] == 10000.0
        assert summary["available_balance"] == 8000.0
        assert summary["total_positions"] == 1
        assert summary["total_unrealized_pnl"] == 1000.0
        assert summary["pending_orders_count"] == 1

    @patch('webapp.api.okx_api.get_user_api')
    def test_get_user_dashboard_no_api_config(self, mock_get_user_api, mock_user, mock_db):
        """测试用户没有API配置的情况"""
        mock_get_user_api.side_effect = Exception("用户API配置不存在")

        result = get_user_dashboard(mock_user, mock_db)

        assert result["code"] == "1"
        assert "获取综合信息失败" in result["msg"]

    def test_calculate_dashboard_summary(self):
        """测试汇总数据计算"""
        # 模拟输入数据
        balance_data = [
            {
                "details": [
                    {
                        "ccy": "USDT",
                        "eq": "10000.0",
                        "availEq": "8000.0"
                    },
                    {
                        "ccy": "BTC",
                        "eq": "0.5",
                        "availEq": "0.3"
                    }
                ]
            }
        ]

        positions_data = [
            {
                "pos": "1.0",
                "upl": "1000.0",
                "realizedPnl": "500.0",
                "margin": "500.0",
                "instType": "SWAP"
            },
            {
                "pos": "0.5",
                "upl": "-200.0",
                "realizedPnl": "100.0",
                "margin": "300.0",
                "instType": "SPOT"
            }
        ]

        pending_orders_data = [
            {"order1": "data1"},
            {"order2": "data2"}
        ]

        # 调用计算函数
        summary = calculate_dashboard_summary(
            balance_data, positions_data, pending_orders_data)

        # 验证计算结果
        assert summary["total_equity"] == 10000.0
        assert summary["available_balance"] == 8000.0
        assert summary["total_positions"] == 2
        assert summary["total_unrealized_pnl"] == 800.0  # 1000 + (-200)
        assert summary["total_realized_pnl"] == 600.0    # 500 + 100
        assert summary["total_margin"] == 800.0          # 500 + 300
        assert summary["pending_orders_count"] == 2

        # 验证持仓分布
        assert "SWAP" in summary["position_breakdown"]
        assert "SPOT" in summary["position_breakdown"]
        assert summary["position_breakdown"]["SWAP"]["count"] == 1
        assert summary["position_breakdown"]["SPOT"]["count"] == 1

        # 验证风险评分计算
        assert summary["risk_score"] > 0
        assert summary["risk_score"] <= 100

    def test_calculate_dashboard_summary_empty_data(self):
        """测试空数据的汇总计算"""
        summary = calculate_dashboard_summary([], [], [])

        assert summary["total_equity"] == 0
        assert summary["available_balance"] == 0
        assert summary["total_positions"] == 0
        assert summary["total_unrealized_pnl"] == 0
        assert summary["total_realized_pnl"] == 0
        assert summary["total_margin"] == 0
        assert summary["pending_orders_count"] == 0
        assert summary["risk_score"] == 0
        assert summary["position_breakdown"] == {}

    def test_calculate_dashboard_summary_zero_equity(self):
        """测试总权益为0的情况"""
        balance_data = [
            {
                "details": [
                    {
                        "ccy": "USDT",
                        "eq": "0.0",
                        "availEq": "0.0"
                    }
                ]
            }
        ]

        positions_data = [
            {
                "pos": "1.0",
                "upl": "1000.0",
                "realizedPnl": "500.0",
                "margin": "500.0",
                "instType": "SWAP"
            }
        ]

        summary = calculate_dashboard_summary(balance_data, positions_data, [])

        assert summary["total_equity"] == 0
        assert summary["risk_score"] == 0  # 总权益为0时风险评分应该为0
