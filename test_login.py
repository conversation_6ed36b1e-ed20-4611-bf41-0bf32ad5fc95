#!/usr/bin/env python3
"""
测试登录API的脚本
"""

import requests
import json

def test_login():
    url = "http://localhost:8002/api/user/login"

    # 测试数据
    login_data = {
        "username": "string",
        "password": "string"
    }

    try:
        print(f"正在测试登录API: {url}")
        print(f"登录数据: {login_data}")

        response = requests.post(url, json=login_data)

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                print("✅ 登录成功！")
                print(f"Token: {data.get('token', 'N/A')}")
                print(f"过期时间: {data.get('exp', 'N/A')}")
            else:
                print(f"❌ 登录失败: {data.get('msg', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_login()
