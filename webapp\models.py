from sqlalchemy import Column, Integer, String, DateTime, Boolean, DECIMAL, create_engine, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
import datetime

Base = declarative_base()


class User(Base):
    __tablename__ = 'user'
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=True)  # 添加email字段
    password = Column(String(128), nullable=False)
    hashed_password = Column(String(255), nullable=True)  # 添加hashed_password字段
    api_key = Column(String(128))
    api_secret = Column(String(128))  # 暂时恢复为原来的类型
    passphrase = Column(String(128))  # 暂时恢复为原来的类型
    # environment = Column(String(20), default='demo')  # 暂时注释掉
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)  # 添加is_verified字段


class AccountBalance(Base):
    __tablename__ = 'account_balance'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=False)
    ccy = Column(String(20))
    balance = Column(DECIMAL(32, 8))
    available = Column(DECIMAL(32, 8))
    frozen = Column(DECIMAL(32, 8))
    updated_at = Column(DateTime, default=datetime.datetime.utcnow)


class Strategy(Base):
    __tablename__ = 'strategy'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=False)
    name = Column(String(100), nullable=False)
    params = Column(JSON)
    status = Column(String(20))  # 回测/实盘/停止
    log = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow,
                        onupdate=datetime.datetime.utcnow)


class Order(Base):
    __tablename__ = 'order'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=False)
    strategy_id = Column(Integer)
    inst_id = Column(String(50))
    side = Column(String(10))  # 买/卖
    price = Column(DECIMAL(32, 8))
    size = Column(DECIMAL(32, 8))
    status = Column(String(20))  # 已提交/已成交/已撤销/失败
    order_type = Column(String(20))  # 限价/市价/止盈止损等
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow,
                        onupdate=datetime.datetime.utcnow)


class AIAnalysis(Base):
    __tablename__ = 'ai_analysis'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=False)
    type = Column(String(20))  # 行情/新闻/情绪/链上
    content = Column(Text)
    suggestion = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)


class MarketKline(Base):
    __tablename__ = 'market_kline'
    id = Column(Integer, primary_key=True)
    inst_id = Column(String(50))
    ts = Column(DateTime)
    open = Column(DECIMAL(32, 8))
    high = Column(DECIMAL(32, 8))
    low = Column(DECIMAL(32, 8))
    close = Column(DECIMAL(32, 8))
    volume = Column(DECIMAL(32, 8))
