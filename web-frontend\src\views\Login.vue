<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h2>{{ isLogin ? '用户登录' : '用户注册' }}</h2>
        <p>{{ isLogin ? '欢迎回来！请登录您的账户' : '创建新账户开始使用' }}</p>
      </div>

      <a-form
        @submit.prevent="isLogin ? onLogin() : onRegister()"
        :model="form"
        class="auth-form"
        layout="vertical"
      >
        <a-form-item label="用户名" name="username" :rules="[{ required: true, message: '请输入用户名' }]">
          <a-input
            v-model:value="form.username"
            placeholder="请输入用户名"
            size="large"
          />
        </a-form-item>

        <a-form-item label="密码" name="password" :rules="[{ required: true, message: '请输入密码' }]">
          <a-input-password
            v-model:value="form.password"
            placeholder="请输入密码"
            size="large"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            :loading="loading"
            size="large"
            block
          >
            {{ isLogin ? '登录' : '注册' }}
          </a-button>
        </a-form-item>

        <div class="auth-switch">
          <span>{{ isLogin ? '还没有账户？' : '已有账户？' }}</span>
          <a @click="toggleMode">{{ isLogin ? '立即注册' : '立即登录' }}</a>
        </div>

        <a-alert
          v-if="error"
          :message="error"
          type="error"
          show-icon
          closable
          @close="error = ''"
        />
      </a-form>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import axios from 'axios'
import { message } from 'ant-design-vue'
import { useUserStore } from '../stores/user'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const router = useRouter()
const form = ref({ username: '', password: '' })
const loading = ref(false)
const error = ref('')
const isLogin = ref(true)

// 切换登录/注册模式
const toggleMode = () => {
  isLogin.value = !isLogin.value
  error.value = ''
  form.value = { username: '', password: '' }
}

// 登录功能
const onLogin = async () => {
  if (!form.value.username || !form.value.password) {
    error.value = '请填写完整的用户名和密码'
    return
  }

  loading.value = true
  error.value = ''

  try {
    console.log('发送登录请求:', form.value)
    const res = await axios.post('/api/user/login', form.value)
    console.log('登录响应:', res.data)

    if (res.data.code === 0) {
      localStorage.setItem('token', res.data.token)
      localStorage.setItem('token_exp', res.data.exp)
      userStore.setUser(form.value.username, res.data.token, res.data.exp)
      message.success('登录成功')

      // 跳转到主页面
      setTimeout(() => {
        router.push('/dashboard')
      }, 500)
    } else {
      error.value = res.data.msg || '登录失败'
      message.error(error.value)
    }
  } catch (e) {
    console.error('登录错误:', e)
    error.value = e.response?.data?.detail || '登录失败，请检查网络连接'
    message.error(error.value)
  }

  loading.value = false
}

// 注册功能
const onRegister = async () => {
  if (!form.value.username || !form.value.password) {
    error.value = '请填写完整的用户名和密码'
    return
  }

  if (form.value.password.length < 6) {
    error.value = '密码长度至少6位'
    return
  }

  loading.value = true
  error.value = ''

  try {
    console.log('发送注册请求:', form.value)
    const res = await axios.post('/api/user/register', form.value)
    console.log('注册响应:', res.data)

    if (res.data.code === 0) {
      message.success('注册成功，请登录')
      isLogin.value = true
      form.value = { username: '', password: '' }
    } else {
      error.value = res.data.msg || '注册失败'
      message.error(error.value)
    }
  } catch (e) {
    console.error('注册错误:', e)
    error.value = e.response?.data?.detail || '注册失败，请检查网络连接'
    message.error(error.value)
  }

  loading.value = false
}
</script>
<style scoped>
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.auth-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.auth-form {
  margin-top: 20px;
}

.auth-form .ant-form-item {
  margin-bottom: 20px;
}

.auth-form .ant-form-item-label > label {
  font-weight: 500;
  color: #333;
}

.auth-switch {
  text-align: center;
  margin-top: 20px;
  color: #666;
}

.auth-switch a {
  color: #1890ff;
  text-decoration: none;
  margin-left: 5px;
  font-weight: 500;
}

.auth-switch a:hover {
  color: #40a9ff;
}

.ant-alert {
  margin-top: 15px;
}
</style>