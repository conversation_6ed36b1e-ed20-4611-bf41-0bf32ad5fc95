#!/usr/bin/env python3
"""
测试认证状态和API访问
"""

import requests
import json

def test_login():
    """测试登录并获取token"""
    print("=== 测试登录 ===")
    
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(
            "http://localhost:8002/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"登录响应状态: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"获取到token: {token[:50]}..." if token else "未获取到token")
            return token
        else:
            print("登录失败")
            return None
            
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_ai_predictions(token):
    """测试AI预测API"""
    print("\n=== 测试AI预测API ===")
    
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        response = requests.get(
            "http://localhost:8002/api/ai/predictions",
            headers=headers
        )
        
        print(f"AI预测API响应状态: {response.status_code}")
        print(f"AI预测API响应内容: {response.text}")
        
    except Exception as e:
        print(f"AI预测API请求失败: {e}")

def test_health():
    """测试健康检查"""
    print("\n=== 测试健康检查 ===")
    
    try:
        response = requests.get("http://localhost:8002/health")
        print(f"健康检查响应状态: {response.status_code}")
        print(f"健康检查响应内容: {response.text}")
        
    except Exception as e:
        print(f"健康检查请求失败: {e}")

def main():
    print("开始测试认证状态...")
    
    # 测试健康检查
    test_health()
    
    # 测试登录
    token = test_login()
    
    # 测试AI预测API
    test_ai_predictions(token)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
