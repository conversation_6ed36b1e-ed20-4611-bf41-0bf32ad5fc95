<template>
  <div class="trading-signals">
    <a-card title="交易信号统计" size="small">
      <div class="signals-grid">
        <!-- EMA 信号 -->
        <div class="signal-row">
          <span class="signal-label">EMA 交叉</span>
          <a-tag :color="emaSignal.color" class="signal-status">{{ emaSignal.status }}</a-tag>
          <span class="signal-value">{{ emaSignal.direction }}</span>
        </div>

        <!-- EMA 状态 -->
        <div class="signal-row">
          <span class="signal-label">EMA 慢线状态</span>
          <a-tag :color="emaSlowStatus.color" class="signal-status">{{ emaSlowStatus.status }}</a-tag>
          <span class="signal-value">{{ emaSlowStatus.value }}</span>
        </div>

        <div class="signal-row">
          <span class="signal-label">EMA 中性线状态</span>
          <a-tag :color="emaMidStatus.color" class="signal-status">{{ emaMidStatus.status }}</a-tag>
          <span class="signal-value">{{ emaMidStatus.value }}</span>
        </div>

        <!-- MA 状态 -->
        <div class="signal-row">
          <span class="signal-label">MA 快线状态</span>
          <a-tag :color="maFastStatus.color" class="signal-status">{{ maFastStatus.status }}</a-tag>
          <span class="signal-value">{{ maFastStatus.value }}</span>
        </div>

        <div class="signal-row">
          <span class="signal-label">MA 中性线状态</span>
          <a-tag :color="maMidStatus.color" class="signal-status">{{ maMidStatus.status }}</a-tag>
          <span class="signal-value">{{ maMidStatus.value }}</span>
        </div>

        <div class="signal-row">
          <span class="signal-label">MA 慢线状态</span>
          <a-tag :color="maSlowStatus.color" class="signal-status">{{ maSlowStatus.status }}</a-tag>
          <span class="signal-value">{{ maSlowStatus.value }}</span>
        </div>

        <!-- RSI 状态 -->
        <div class="signal-row">
          <span class="signal-label">RSI 超买超卖</span>
          <a-tag :color="rsiOverboughtStatus.color" class="signal-status">{{ rsiOverboughtStatus.status }}</a-tag>
          <span class="signal-value">{{ rsiOverboughtStatus.value }}</span>
        </div>

        <div class="signal-row">
          <span class="signal-label">RSI 状态</span>
          <a-tag :color="rsiStatus.color" class="signal-status">{{ rsiStatus.status }}</a-tag>
          <span class="signal-value">{{ rsiStatus.direction }}</span>
        </div>

        <!-- MACD 信号 -->
        <div class="signal-row">
          <span class="signal-label">MACD 信号</span>
          <a-tag :color="macdSignal.color" class="signal-status">{{ macdSignal.status }}</a-tag>
          <span class="signal-value">{{ macdSignal.value }}</span>
        </div>

        <!-- 熊猫指标 -->
        <div class="signal-row">
          <span class="signal-label">熊猫多空指标</span>
          <a-tag :color="pandaLongShort.color" class="signal-status">{{ pandaLongShort.status }}</a-tag>
          <span class="signal-value">{{ pandaLongShort.direction }}</span>
        </div>

        <div class="signal-row">
          <span class="signal-label">熊猫能量指标</span>
          <a-tag :color="pandaEnergy.color" class="signal-status">{{ pandaEnergy.status }}</a-tag>
          <span class="signal-value">{{ pandaEnergy.direction }}</span>
        </div>

        <!-- 超级趋势 -->
        <div class="signal-row">
          <span class="signal-label">超级趋势指标</span>
          <a-tag :color="superTrend.color" class="signal-status">{{ superTrend.status }}</a-tag>
          <span class="signal-value">{{ superTrend.value }}</span>
        </div>

        <!-- 统计汇总 -->
        <div class="signal-row summary-row">
          <span class="signal-label">多头空头统计</span>
          <a-tag color="green" class="signal-status">{{ bullBearStats.bulls }}/{{ bullBearStats.bears }}</a-tag>
          <span class="signal-value">{{ bullBearStats.direction }}</span>
        </div>
      </div>

      <!-- 综合建议 -->
      <div class="overall-recommendation">
        <a-divider />
        <div class="recommendation-header">
          <h4>综合建议</h4>
          <a-tag :color="overallRecommendation.color" size="large">
            {{ overallRecommendation.action }}
          </a-tag>
        </div>
        <p class="recommendation-text">{{ overallRecommendation.reason }}</p>
        <div class="recommendation-stats">
          <span>多头信号: {{ signalStats.bullish }}</span>
          <span>空头信号: {{ signalStats.bearish }}</span>
          <span>中性信号: {{ signalStats.neutral }}</span>
          <span>信号强度: {{ signalStats.strength }}%</span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  chartData: {
    type: Array,
    default: () => []
  },
  technicalIndicators: {
    type: Object,
    default: () => ({})
  },
  currentPrice: {
    type: Object,
    default: () => null
  }
})

// 响应式数据
const signals = ref({})

// 计算EMA信号
const emaSignal = computed(() => {
  if (!props.technicalIndicators.ma5 || !props.technicalIndicators.ma10) {
    return { status: '无数据', color: 'default', direction: '-' }
  }

  const ma5 = props.technicalIndicators.ma5
  const ma10 = props.technicalIndicators.ma10

  if (ma5 > ma10) {
    return { status: '金叉', color: 'green', direction: '多头' }
  } else {
    return { status: '死叉', color: 'red', direction: '空头' }
  }
})

// 计算EMA状态
const emaSlowStatus = computed(() => {
  const currentPrice = parseFloat(props.currentPrice?.last || 0)
  const ma20 = props.technicalIndicators.ma20 || 0

  if (!currentPrice || !ma20) {
    return { status: '无数据', color: 'default', value: '-' }
  }

  if (currentPrice > ma20) {
    return { status: '多头', color: 'green', value: ma20.toFixed(2) }
  } else {
    return { status: '空头', color: 'red', value: ma20.toFixed(2) }
  }
})

const emaMidStatus = computed(() => {
  const currentPrice = parseFloat(props.currentPrice?.last || 0)
  const ma10 = props.technicalIndicators.ma10 || 0

  if (!currentPrice || !ma10) {
    return { status: '无数据', color: 'default', value: '-' }
  }

  if (currentPrice > ma10) {
    return { status: '多头', color: 'green', value: ma10.toFixed(2) }
  } else {
    return { status: '空头', color: 'red', value: ma10.toFixed(2) }
  }
})

// 计算MA状态
const maFastStatus = computed(() => {
  const currentPrice = parseFloat(props.currentPrice?.last || 0)
  const ma5 = props.technicalIndicators.ma5 || 0

  if (!currentPrice || !ma5) {
    return { status: '无数据', color: 'default', value: '-' }
  }

  if (currentPrice > ma5) {
    return { status: '多头', color: 'green', value: ma5.toFixed(2) }
  } else {
    return { status: '空头', color: 'red', value: ma5.toFixed(2) }
  }
})

const maMidStatus = computed(() => {
  const currentPrice = parseFloat(props.currentPrice?.last || 0)
  const ma10 = props.technicalIndicators.ma10 || 0

  if (!currentPrice || !ma10) {
    return { status: '无数据', color: 'default', value: '-' }
  }

  if (currentPrice > ma10) {
    return { status: '多头', color: 'green', value: ma10.toFixed(2) }
  } else {
    return { status: '空头', color: 'red', value: ma10.toFixed(2) }
  }
})

const maSlowStatus = computed(() => {
  const currentPrice = parseFloat(props.currentPrice?.last || 0)
  const ma20 = props.technicalIndicators.ma20 || 0

  if (!currentPrice || !ma20) {
    return { status: '无数据', color: 'default', value: '-' }
  }

  if (currentPrice > ma20) {
    return { status: '多头', color: 'green', value: ma20.toFixed(2) }
  } else {
    return { status: '空头', color: 'red', value: ma20.toFixed(2) }
  }
})

// 计算RSI状态
const rsiOverboughtStatus = computed(() => {
  const rsi = props.technicalIndicators.rsi || 50

  if (rsi > 70) {
    return { status: 'RSI 中立', color: 'orange', value: rsi.toFixed(2) }
  } else if (rsi < 30) {
    return { status: 'RSI 超卖', color: 'green', value: rsi.toFixed(2) }
  } else {
    return { status: 'RSI 中立', color: 'blue', value: rsi.toFixed(2) }
  }
})

const rsiStatus = computed(() => {
  const rsi = props.technicalIndicators.rsi || 50

  if (rsi > 50) {
    return { status: '中立', color: 'blue', direction: '观望' }
  } else {
    return { status: '中立', color: 'blue', direction: '观望' }
  }
})

// 计算MACD信号
const macdSignal = computed(() => {
  const macd = props.technicalIndicators.macd || 0

  if (macd > 0) {
    return { status: '中立', color: 'blue', value: macd.toFixed(2) }
  } else {
    return { status: '中立', color: 'blue', value: macd.toFixed(2) }
  }
})

// 熊猫指标（模拟）
const pandaLongShort = computed(() => {
  const ma5 = props.technicalIndicators.ma5 || 0
  const ma20 = props.technicalIndicators.ma20 || 0

  if (ma5 > ma20) {
    return { status: '多头', color: 'green', direction: '考虑买入' }
  } else {
    return { status: '空头', color: 'red', direction: '考虑卖出' }
  }
})

const pandaEnergy = computed(() => {
  const rsi = props.technicalIndicators.rsi || 50

  if (rsi > 60) {
    return { status: '买入', color: 'green', direction: '考虑买入' }
  } else if (rsi < 40) {
    return { status: '卖出', color: 'red', direction: '考虑卖出' }
  } else {
    return { status: '中立', color: 'blue', direction: '观望' }
  }
})

// 超级趋势指标（模拟）
const superTrend = computed(() => {
  const currentPrice = parseFloat(props.currentPrice?.last || 0)
  const ma20 = props.technicalIndicators.ma20 || 0

  if (!currentPrice || !ma20) {
    return { status: '无数据', color: 'default', value: '-' }
  }

  const trendValue = ma20 * 1.02 // 简化的超级趋势计算

  if (currentPrice > trendValue) {
    return { status: '上升趋势', color: 'orange', value: trendValue.toFixed(2) }
  } else {
    return { status: '下降趋势', color: 'red', value: trendValue.toFixed(2) }
  }
})

// 多头空头统计
const bullBearStats = computed(() => {
  let bulls = 0
  let bears = 0

  // 统计各个指标的多空信号
  const indicators = [
    emaSignal.value,
    emaSlowStatus.value,
    emaMidStatus.value,
    maFastStatus.value,
    maMidStatus.value,
    maSlowStatus.value,
    pandaLongShort.value,
    pandaEnergy.value
  ]

  indicators.forEach(indicator => {
    if (indicator.status === '多头' || indicator.status === '金叉' || indicator.status === '买入' || indicator.status === '上升趋势') {
      bulls++
    } else if (indicator.status === '空头' || indicator.status === '死叉' || indicator.status === '卖出' || indicator.status === '下降趋势') {
      bears++
    }
  })

  const direction = bulls > bears ? '多头' : bears > bulls ? '空头' : '中性'

  return { bulls, bears, direction }
})

// 信号统计
const signalStats = computed(() => {
  const stats = { bullish: 0, bearish: 0, neutral: 0 }

  const allSignals = [
    emaSignal.value,
    emaSlowStatus.value,
    emaMidStatus.value,
    maFastStatus.value,
    maMidStatus.value,
    maSlowStatus.value,
    rsiOverboughtStatus.value,
    rsiStatus.value,
    macdSignal.value,
    pandaLongShort.value,
    pandaEnergy.value,
    superTrend.value
  ]

  allSignals.forEach(signal => {
    if (signal.status === '多头' || signal.status === '金叉' || signal.status === '买入' || signal.status === '上升趋势') {
      stats.bullish++
    } else if (signal.status === '空头' || signal.status === '死叉' || signal.status === '卖出' || signal.status === '下降趋势') {
      stats.bearish++
    } else {
      stats.neutral++
    }
  })

  const total = stats.bullish + stats.bearish + stats.neutral
  const strength = total > 0 ? Math.round((Math.max(stats.bullish, stats.bearish) / total) * 100) : 0

  return { ...stats, strength }
})

// 综合建议
const overallRecommendation = computed(() => {
  const { bullish, bearish, neutral, strength } = signalStats.value

  if (bullish > bearish && strength > 60) {
    return {
      action: '强烈买入',
      color: 'green',
      reason: `多头信号占优势，信号强度${strength}%，建议积极买入。`
    }
  } else if (bullish > bearish && strength > 40) {
    return {
      action: '买入',
      color: 'green',
      reason: `多头信号较多，信号强度${strength}%，可以考虑买入。`
    }
  } else if (bearish > bullish && strength > 60) {
    return {
      action: '强烈卖出',
      color: 'red',
      reason: `空头信号占优势，信号强度${strength}%，建议积极卖出。`
    }
  } else if (bearish > bullish && strength > 40) {
    return {
      action: '卖出',
      color: 'red',
      reason: `空头信号较多，信号强度${strength}%，可以考虑卖出。`
    }
  } else {
    return {
      action: '观望',
      color: 'blue',
      reason: `多空信号相对平衡，信号强度${strength}%，建议观望等待更明确的信号。`
    }
  }
})
</script>

<style scoped>
.trading-signals {
  width: 100%;
}

.signals-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.signal-row {
  display: grid;
  grid-template-columns: 1fr auto auto;
  align-items: center;
  gap: 12px;
  padding: 10px 14px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #d9d9d9;
  margin-bottom: 4px;
  transition: all 0.2s ease;
}

.signal-row:hover {
  background: #f0f0f0;
}

.summary-row {
  border-left-color: #1890ff;
  background: #e6f7ff;
}

.signal-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.signal-status {
  min-width: 60px;
  text-align: center;
  font-weight: bold;
}

.signal-value {
  font-size: 12px;
  color: #999;
  text-align: right;
  min-width: 80px;
}

.overall-recommendation {
  margin-top: 16px;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.recommendation-header h4 {
  margin: 0;
  color: #333;
}

.recommendation-text {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.recommendation-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.recommendation-stats span {
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>
