#!/usr/bin/env python3
"""
直接更新API密钥
"""

import sqlite3

def update_api_keys():
    # 您需要提供完整的API信息
    api_key = input("请输入您的OKX API Key: ").strip()
    passphrase = input("请输入您的OKX Passphrase: ").strip()
    
    # 使用您提供的Secret Key
    api_secret = "B036379F9F56E5A83FB30EF27D7C57CF"
    
    if not api_key or not passphrase:
        print("❌ 请提供完整的API信息")
        return
    
    # 连接数据库
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    # 更新admin用户的API配置
    cursor.execute('''
        UPDATE user 
        SET api_key = ?, api_secret = ?, passphrase = ?
        WHERE username = 'admin'
    ''', (api_key, api_secret, passphrase))
    
    if cursor.rowcount > 0:
        conn.commit()
        print(f"✅ API密钥配置成功！")
        print(f"   API Key: {api_key}")
        print(f"   Secret Key: {api_secret[:8]}****")
        print(f"   Passphrase: {passphrase[:4]}****")
        
        # 验证配置
        cursor.execute('SELECT api_key, api_secret, passphrase FROM user WHERE username = ?', ('admin',))
        result = cursor.fetchone()
        if result:
            print(f"✅ 数据库验证成功")
        else:
            print("❌ 数据库验证失败")
    else:
        print("❌ 配置失败：找不到admin用户")
    
    conn.close()

if __name__ == "__main__":
    update_api_keys()
